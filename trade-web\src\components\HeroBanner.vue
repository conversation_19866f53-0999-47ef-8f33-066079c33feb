<template>
  <div class="hero-banner">
    <!-- 调试信息 -->
    <div v-if="!banners || banners.length === 0" class="debug-info">
      <p>轮播图数据: {{ banners }}</p>
      <p>数据长度: {{ banners ? banners.length : 'undefined' }}</p>
    </div>
    
    <!-- 轮播图 -->
    <el-carousel 
      v-if="banners && banners.length > 0"
      :interval="5000" 
      arrow="always" 
      height="100%" 
      class="carousel-container"
    >
      <el-carousel-item v-for="item in banners" :key="item.id">
        <a :href="item.deeplink || '#'" target="_blank" rel="noopener noreferrer" class="banner-link">
          <img :src="item.image" :alt="item.name" class="banner-image"/>
          <div class="banner-caption">
            <h3>{{ item.name }}</h3>
          </div>
        </a>
      </el-carousel-item>
    </el-carousel>
    
    <!-- 空状态 -->
    <div v-else class="empty-banner">
      <p>暂无轮播图数据</p>
    </div>
  </div>
</template>

<script>
import { watch } from 'vue'

export default {
  name: 'HeroBanner',
  props: {
    banners: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  setup(props) {
    console.log('HeroBanner 组件初始化，接收到的 banners:', props.banners)
    
    watch(() => props.banners, (newBanners) => {
      console.log('HeroBanner banners 数据更新:', newBanners)
    }, { immediate: true })
    
    return {}
  }
}
</script>

<style lang="scss" scoped>
.hero-banner {
  width: 100%;
  height: 480px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  background-color: #f5f5f5; /* 添加背景色便于调试 */
}

:deep(.el-carousel) {
  width: 100%;
  height: 100% !important;
}

:deep(.el-carousel__container) {
  width: 100%;
  height: 100% !important;
}

:deep(.el-carousel__item) {
  width: 100%;
  height: 100% !important;
}

.banner-link {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  text-decoration: none;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  display: block;
}

.banner-link:hover .banner-image {
  transform: scale(1.05);
}

.banner-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%);
  color: white;
  
  h3 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  }
}

/* Element Plus carousel 按钮样式 */
:deep(.el-carousel__arrow) {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border: none;
  width: 40px;
  height: 40px;
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(255, 255, 255, 0.9);
}

/* Element Plus carousel 指示器样式 */
:deep(.el-carousel__indicators) {
  bottom: 20px;
}

:deep(.el-carousel__indicator) {
  width: 12px;
  height: 4px;
}

:deep(.el-carousel__button) {
  width: 12px;
  height: 4px;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.5);
}

:deep(.el-carousel__indicator.is-active .el-carousel__button) {
  background-color: white;
}

.debug-info {
  padding: 20px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin: 20px;
  
  p {
    margin: 5px 0;
    font-family: monospace;
    font-size: 14px;
  }
}

.empty-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f9fa;
  color: #666;
  
  p {
    font-size: 18px;
    margin: 0;
  }
}

@media (max-width: 768px) {
  .hero-banner {
    height: 320px;
  }
  
  .banner-caption h3 {
    font-size: 18px;
  }
}
</style> 