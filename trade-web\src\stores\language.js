import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLanguageStore = defineStore('language', () => {
  const currentLang = ref('zh') // 默认中文
  
  const setLanguage = (lang) => {
    currentLang.value = lang
    // 保存到本地存储
    localStorage.setItem('language', lang)
  }
  
  const initLanguage = () => {
    // 从本地存储获取语言设置
    const savedLang = localStorage.getItem('language')
    if (savedLang) {
      currentLang.value = savedLang
    }
  }
  
  return {
    currentLang,
    setLanguage,
    initLanguage
  }
}) 