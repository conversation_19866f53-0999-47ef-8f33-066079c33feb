# 商户详情API对接文档

## 概述

本文档描述了如何将商户详情API接口与商户主页进行对接的完整实现方案。

## API接口信息

- **接口路径**: `/v1/public/merchant/{id}`
- **请求方法**: GET
- **参数**: 商户ID (路径参数)
- **返回格式**: JSON

### 返回数据结构

```json
{
    "code": 0,
    "msg": "",
    "data": {
        "id": 1,
        "name": "测试商户",
        "attributes": [
            {
                "id": 1,
                "merchant_id": 1,
                "attribute_id": 1,
                "value": "法律服务",
                "language": "zh-CN",
                "attribute": {
                    "id": 1,
                    "name": "企业名称",
                    "description": "企业名称",
                    "status": 1,
                    "type": "base",
                    "value_type": "string",
                    "is_multi_text": 0,
                    "is_i18n": 1,
                    "is_image": 0,
                    "is_video": 0
                }
            }
            // ... 更多属性
        ]
    }
}
```

## 实现的功能

### 1. API调用模块 (`src/api/merchant.js`)

- `getMerchantDetail(merchantId)`: 获取商户详情
- `parseMerchantAttributes(attributes)`: 解析商户属性数据
- `formatMerchantData(rawData)`: 格式化为组件所需数据结构

### 2. 数据映射规则

根据 `attribute.name` 字段映射数据：

#### 基础信息 (type: "base")
- `企业名称` → 公司名称
- `Logo` → 公司Logo
- `主页图片` → 主页展示图片
- `简介` → 公司简介
- `轮播图` → 轮播图数组 (JSON格式)

#### 企业档案 (type: "profile")
- `BusinessType` → 企业类型
- `MainProducts` → 主要产品
- `Number of Employees` → 员工数量
- `Year of Establishment` → 成立时间
- `Registered Capital` → 注册资本
- `Registered Capital Currency` → 货币单位
- `Profile` → 详细档案

#### 联系方式 (type: "contact")
- `Adddress` → 地址 (注意API中是三个d)
- `Telohone` → 电话 (注意API中拼写错误)
- `Website` → 官网
- `HomePage` → 站内主页
- `Lat` → 纬度
- `Lng` → 经度

### 3. 组件集成 (`src/views/CompanyHomeView.vue`)

#### 新增状态管理
```javascript
const loading = ref(false)        // 加载状态
const merchantData = ref(null)    // API数据
const error = ref(null)          // 错误信息
```

#### 数据加载函数
```javascript
const loadMerchantData = async () => {
  // 从API获取数据并格式化
}
```

#### 响应式数据绑定
- 优先使用API数据，降级到模拟数据
- 支持加载状态和错误处理
- 自动监听路由参数变化

## 使用方法

### 1. 开发环境配置

创建 `.env.development` 文件：
```
VITE_APP_API_BASE_URL=http://localhost:8090
```

### 2. 生产环境配置

创建 `.env.production` 文件：
```
VITE_APP_API_BASE_URL=https://api.yourdomain.com
```

### 3. 访问商户主页

访问路径：`/company/{merchantId}`

例如：`http://localhost:5173/company/1`

### 4. API测试页面

访问 `/test-merchant` 可以测试API对接功能：
- 输入商户ID
- 查看API返回的原始数据
- 查看解析后的格式化数据
- 直接跳转到商户主页

## 特殊处理

### 1. 轮播图数据
轮播图数据以JSON字符串格式存储，需要解析：
```javascript
const bannerUrls = JSON.parse(value)
merchantData.banners = bannerUrls.map((url, index) => ({
  id: index + 1,
  image: url,
  title: `轮播图 ${index + 1}`,
  // ...
}))
```

### 2. 默认值处理
当API数据为空时，提供合理的默认值：
```javascript
logo: parsedData.logo || '/src/assets/logo.png'
rating: 4.8 // 默认评级
```

### 3. 错误处理
- 网络请求失败
- API返回错误码
- 数据解析异常
- 用户友好的错误提示

## 注意事项

1. **API字段拼写**: 注意API中的拼写错误（如 `Adddress`, `Telohone`）
2. **数据类型**: 轮播图是JSON数组格式，需要特殊处理
3. **多语言**: 当前支持中文，后续可扩展多语言
4. **图片URL**: 确保图片URL的正确性和可访问性
5. **错误降级**: API失败时降级到模拟数据，保证页面正常显示

## 扩展建议

1. **缓存机制**: 添加数据缓存，减少重复请求
2. **多语言支持**: 根据 `language` 字段支持多语言切换
3. **图片懒加载**: 对轮播图等图片资源进行懒加载优化
4. **SEO优化**: 添加meta标签和结构化数据
5. **性能监控**: 添加API请求性能监控

## 测试验证

1. 启动后端服务 (端口8090)
2. 启动前端开发服务器
3. 访问 `/test-merchant` 测试API
4. 访问 `/company/1` 查看实际效果
5. 检查网络请求和数据映射是否正确
