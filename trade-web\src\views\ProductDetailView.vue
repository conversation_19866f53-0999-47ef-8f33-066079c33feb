<template>
  <div class="product-detail">
    <!-- 固定头部 -->
    <HeaderNav />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 面包屑导航 -->
        <!-- <div class="breadcrumb">
          <span @click="$router.push('/')" class="breadcrumb-item">
            {{ currentLang === 'zh' ? '首页' : 'Home' }}
          </span>
          <span class="separator">/</span>
          <span v-if="currentCategory" @click="goToCategory" class="breadcrumb-item">
            {{ currentLang === 'zh' ? currentCategory.name : currentCategory.nameEn }}
          </span>
          <span class="separator">/</span>
          <span v-if="product" class="breadcrumb-item active">
            {{ currentLang === 'zh' ? product.name : product.nameEn }}
          </span>
        </div> -->
        
        <!-- 产品主要信息 -->
        <div v-if="product" class="product-main">
          <div class="product-layout">
            <!-- 左侧：产品图片 -->
            <div class="product-images">
              <ProductImageGallery 
                :images="product.images || [product.image]"
                :product-name="currentLang === 'zh' ? product.name : product.nameEn"
                class="product-gallery"
              />
            </div>
            
            <!-- 右侧：产品信息 -->
            <div class="product-info">
              <h1 class="product-title">
                {{ currentLang === 'zh' ? product.name : product.nameEn }}
              </h1>
              
              <div class="price-section">
                <div v-if="product.pricesList && product.pricesList.length > 0" class="price-list">
                  <div v-for="(priceItem, index) in product.pricesList" :key="index" class="price-item">
                    <span class="price-text">
                      {{ currentLang === 'zh' ? '单价：' : 'Unit Price: ' }}{{ priceItem.price_unit }} {{ priceItem.currency }}，
                      {{ currentLang === 'zh' ? '起订量：' : 'Min Order: ' }}{{ priceItem.order_min }} - {{ priceItem.order_max }}
                    </span>
                  </div>
                </div>
                <div v-else class="price-empty">{{ currentLang === 'zh' ? '价格面议' : 'Negotiable' }}</div>
              </div>
<!--               
              <div class="product-meta">
                <div class="meta-item">
                  <span class="meta-label">{{ currentLang === 'zh' ? '起购量：' : 'Min Order:' }}</span>
                  <span class="meta-value">{{ product.minOrder }} {{ currentLang === 'zh' ? product.minOrderUnit : product.minOrderUnitEn }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">{{ currentLang === 'zh' ? '库存：' : 'Stock:' }}</span>
                  <span class="meta-value">{{ product.stock }}</span>
                </div>
              </div> -->
              
              <div class="product-summary">
                <h3>{{ currentLang === 'zh' ? '产品简介' : 'Product Summary' }}</h3>
                <p>{{ currentLang === 'zh' ? product.description : product.descriptionEn }}</p>
              </div>
              
              <div v-if="product.specList && product.specList.length" class="specifications">
                <h3>{{ currentLang === 'zh' ? '规格参数' : 'Specifications' }}</h3>
                <ul class="spec-inline">
                  <li v-for="(spec, i) in product.specList" :key="i">
                    {{ spec.label }}：<b>{{ spec.value }}</b>
                  </li>
                </ul>
              </div>
              
              <!-- 供应商信息 -->
              <div v-if="company" class="supplier-info">
                <h3>{{ currentLang === 'zh' ? '供应商信息' : 'Supplier Information' }}</h3>
                <div class="supplier-card">
                  <div class="supplier-header">
                    <img :src="company.avatar" :alt="company.name" class="supplier-avatar" @click="viewCompanyProfile" style="cursor: pointer;" />
                    <div class="supplier-basic">
                      <h4 class="supplier-name" @click="viewCompanyProfile" style="cursor: pointer;">
                        {{ currentLang === 'zh' ? company.name : company.nameEn }}
                      </h4>
                      <div class="supplier-rating">
                        <div class="stars">
                          <span v-for="i in 5" :key="i" class="star" :class="{ filled: i <= Math.floor(company.rating) }">★</span>
                          <span class="rating-number">{{ company.rating }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="supplier-details">
                    <div class="detail-row">
                      <el-icon><LocationFilled /></el-icon>
                      <span>{{ currentLang === 'zh' ? company.address : company.addressEn }}</span>
                    </div>
                    <div class="detail-row">
                      <el-icon><OfficeBuilding /></el-icon>
                      <span>{{ currentLang === 'zh' ? company.businessType : company.businessTypeEn }}</span>
                    </div>
                  </div> -->
                  
                  <!-- 添加查看公司主页按钮 -->
                  <div class="view-company-btn-wrapper">
                    <el-button type="primary" plain size="small" @click="viewCompanyProfile">
                      <el-icon><Office /></el-icon>
                      {{ currentLang === 'zh' ? '查看公司主页' : 'View Company Profile' }}
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button type="primary" size="large" @click="contactSupplier" class="contact-btn">
                  <el-icon><Phone /></el-icon>
                  {{ currentLang === 'zh' ? '立即联系' : 'Contact Now' }}
                </el-button>
                <el-button size="large" @click="addToInquiry" class="inquiry-btn">
                  <el-icon><ShoppingCart /></el-icon>
                  {{ currentLang === 'zh' ? '询价单' : 'Inquiry Cart' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 产品详情（无公司信息标签） -->
        <div v-if="product" class="product-detail-section">
          <div class="detail-header">
            <h2 class="section-title">{{ currentLang === 'zh' ? '产品详情' : 'Product Details' }}</h2>
            <div class="section-underline"></div>
          </div>
          <div class="rich-content" v-html="currentLang === 'zh' ? product.detailDescription : product.detailDescriptionEn"></div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>{{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}</p>
        </div>
        
        <!-- 产品未找到 -->
        <div v-if="!loading && !product" class="not-found">
          <el-icon><DocumentDelete /></el-icon>
          <h2>{{ currentLang === 'zh' ? '产品未找到' : 'Product Not Found' }}</h2>
          <p>{{ currentLang === 'zh' ? '抱歉，您访问的产品不存在。' : 'Sorry, the product you are looking for does not exist.' }}</p>
          <el-button type="primary" @click="$router.push('/')">
            {{ currentLang === 'zh' ? '返回首页' : 'Back to Home' }}
          </el-button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { LocationFilled, OfficeBuilding, Phone, ShoppingCart, Message, Loading, DocumentDelete } from '@element-plus/icons-vue'
import HeaderNav from '../components/HeaderNav.vue'
import ProductImageGallery from '../components/ProductImageGallery.vue'
import { useLanguageStore } from '../stores/language.js'
import { getProductDetail } from '../api/product.js'

export default {
  name: 'ProductDetailView',
  components: {
    HeaderNav,
    ProductImageGallery
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const languageStore = useLanguageStore()
    
    const loading = ref(true)
    const activeTab = ref('description')
    const productData = ref(null)
    
    const currentLang = computed(() => languageStore.currentLang)
    
    // 从API数据中获取属性值的辅助函数（使用attribute_id）
    const getAttributeById = (attributes, attributeId) => {
      if (!attributes) return null
      const attribute = attributes.find(attr => attr.attribute_id === attributeId)
      return attribute ? attribute.value : null
    }
    
    // 解析JSON字符串属性（如图片数组）
    const parseJsonAttributeById = (attributes, attributeId) => {
      const value = getAttributeById(attributes, attributeId)
      if (!value) return []
      try {
        return JSON.parse(value)
      } catch (e) {
        return []
      }
    }
    
    // 价格不再汇总为单值，直接在UI渲染所有条目
    
    // 处理后的产品数据
    const product = computed(() => {
      if (!productData.value) return null
      
      const { product: productInfo, prices, attributes } = productData.value
      
      // 确保基础数据存在
      if (!productInfo) return null
      
      // 获取图片数组
      const mainImage = getAttributeById(attributes, 4) // attribute_id: 4 = 主图
      const imagesArray = parseJsonAttributeById(attributes, 5) // attribute_id: 5 = 图片
      const allImages = mainImage ? [mainImage, ...imagesArray] : imagesArray

      // 规格：从属性中简化输出 label+value
      const specRaw = getAttributeById(attributes, 7) // attribute_id: 7 = Specification
      const specList = []
      if (specRaw) {
        // 若为JSON数组或键值对字符串，尽量解析；否则当作单条规格
        try {
          const parsed = JSON.parse(specRaw)
          if (Array.isArray(parsed)) {
            parsed.forEach((v, i) => specList.push({ label: currentLang.value === 'zh' ? `规格${i + 1}` : `Spec ${i + 1}`, value: String(v) }))
          } else if (parsed && typeof parsed === 'object') {
            Object.entries(parsed).forEach(([k, v]) => specList.push({ label: String(k), value: String(v) }))
          } else {
            specList.push({ label: currentLang.value === 'zh' ? '规格' : 'Spec', value: String(specRaw) })
          }
        } catch {
          specList.push({ label: currentLang.value === 'zh' ? '规格' : 'Spec', value: String(specRaw) })
        }
      }
      
      return {
        id: productInfo.id,
        name: getAttributeById(attributes, 1) || productInfo.name || '未知产品', // attribute_id: 1 = 商品名称
        nameEn: getAttributeById(attributes, 1) || productInfo.name || 'Unknown Product',
        pricesList: prices || [],
        image: mainImage,
        images: allImages,
        description: getAttributeById(attributes, 2) || '暂无描述', // attribute_id: 2 = 亮点
        descriptionEn: getAttributeById(attributes, 2) || 'No description available',
        detailDescription: getAttributeById(attributes, 6) || '暂无详细描述', // attribute_id: 6 = 规格(HTML格式)
        detailDescriptionEn: getAttributeById(attributes, 6) || 'No detailed description available',
        categoryId: productInfo.category_id || 0,
        companyId: productInfo.merchant_id || 0,
        specList,
        stock: '现货供应'
      }
    })
    
    // 模拟公司信息（暂时使用固定数据，后续可以通过merchant_id获取）
    const company = computed(() => {
      if (!product.value) return null
      return {
        id: product.value.companyId,
        name: '供应商名称',
        nameEn: 'Supplier Name',
        avatar: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80',
        rating: 4.5,
        phone: '+86 138-0000-0000',
        email: '<EMAIL>',
        companyDescription: '专业的产品供应商，提供优质的产品和服务。',
        companyDescriptionEn: 'Professional product supplier providing quality products and services.',
        companyImages: [
          'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop',
          'https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=300&fit=crop'
        ],
        mainMarkets: ['中国', '东南亚', '欧洲'],
        mainMarketsEn: ['China', 'Southeast Asia', 'Europe']
      }
    })
    
    // 分类信息（暂时简化处理）
    const currentCategory = computed(() => {
      if (!product.value) return null
      return {
        id: product.value.categoryId,
        name: '产品分类',
        nameEn: 'Product Category'
      }
    })
    
    // 加载产品详情数据
    const loadProductDetail = async () => {
      try {
        loading.value = true
        const productId = route.params.productId
        console.log('正在加载产品详情，ID:', productId)
        
        const response = await getProductDetail(productId)
        console.log('产品详情API响应:', response)
        
        if (response && response.code === 0) {
          productData.value = response.data
          console.log('产品数据已设置:', productData.value)
        } else {
          console.error('获取产品详情失败:', response)
          productData.value = null
        }
      } catch (error) {
        console.error('加载产品详情时发生错误:', error)
        productData.value = null
      } finally {
        loading.value = false
      }
    }
    
    // 跳转到分类页面
    const goToCategory = () => {
      if (currentCategory.value) {
        router.push(`/category/${currentCategory.value.id}`)
      }
    }
    
    // 联系供应商
    const contactSupplier = () => {
      if (company.value) {
        router.push(`/contact-supplier/${company.value.id}`)
      } else {
        console.log('供应商信息不可用')
      }
    }
    
    // 添加到询价单
    const addToInquiry = () => {
      console.log('添加到询价单')
      // 这里可以添加询价单逻辑
    }
    
    // 跳转到公司页面
    const viewCompanyProfile = () => {
      if (company.value) {
        router.push(`/company/${company.value.id}`)
      }
    }
    
    // 初始化
    onMounted(() => {
      languageStore.initLanguage()
      loadProductDetail()
    })
    
    // 监听路由变化
    watch(() => route.params.productId, () => {
      loadProductDetail()
    })
    
    return {
      loading,
      activeTab,
      currentLang,
      product,
      company,
      currentCategory,
      goToCategory,
      contactSupplier,
      addToInquiry,
      viewCompanyProfile,
      LocationFilled,
      OfficeBuilding,
      Phone,
      ShoppingCart,
      Loading,
      DocumentDelete
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.main-content {
  margin-top: 30px;
  padding-top: 100px;
  padding-bottom: 30px;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
  }
}

.breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;
  
  .breadcrumb-item {
    color: #6b7280;
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: #2563eb;
    }
    
    &.active {
      color: #1f2937;
      font-weight: 500;
      cursor: default;
    }
  }
  
  .separator {
    margin: 0 8px;
    color: #d1d5db;
  }
}

.product-main {
  margin-bottom: 100px;
  
  .product-layout {
    display: grid;
    grid-template-columns: 0.9fr 1.1fr;
    gap: 30px;
    align-items: flex-start;
  }
}

.product-images {
  position: sticky;
  top: 120px;
  height: auto;
  max-height: calc(100vh - 180px);
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.product-info {
  .product-title {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 16px 0;
    line-height: 1.2;
  }
  
  .price-section {
    margin-bottom: 16px;

    .price-list {
      .price-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .price-text {
          font-size: 18px;
          font-weight: 600;
          color: #eb2535;
          line-height: 1.4;
          display: block;
        }
      }
    }

    .price-empty {
      color: #6b7280;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .product-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 18px;
    
    .meta-item {
      padding: 12px;
      background: white;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      
      .meta-label {
        display: block;
        font-size: 13px;
        color: #6b7280;
        margin-bottom: 2px;
      }
      
      .meta-value {
        font-size: 15px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
  
  .product-summary {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 6px 0;
    }
    
    p {
      color: #4b5563;
      line-height: 1.4;
      margin: 0;
      font-size: 14px;
    }
  }
  
  .specifications {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 6px 0;
    }
    
    .spec-inline {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-wrap: wrap;
      gap: 8px 12px;
    }
    .spec-inline li {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 6px 10px;
      font-size: 14px;
      color: #374151;
    }
    .spec-inline li b { color: #2563eb; }
  }
  
  .supplier-info {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 6px 0;
    }
    
    .supplier-card {
      background: white;
      border-radius: 10px;
      padding: 12px;
      border: 1px solid #e5e7eb;
      
      .supplier-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 10px;
        
        .supplier-avatar {
          width: 48px;
          height: 48px;
          border-radius: 10px;
          object-fit: cover;
          transition: all 0.3s ease;
          
          &:hover {
            opacity: 0.8;
            transform: scale(1.05);
          }
        }
        
        .supplier-basic {
          flex: 1;
          
          .supplier-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 6px 0;
            transition: all 0.3s ease;
            
            &:hover {
              color: #2563eb;
            }
          }
          
          .supplier-rating .stars {
            display: flex;
            align-items: center;
            gap: 2px;
            
            .star {
              color: #d1d5db;
              font-size: 14px;
              
              &.filled {
                color: #fbbf24;
              }
            }
            
            .rating-number {
              margin-left: 6px;
              font-size: 13px;
              color: #6b7280;
              font-weight: 500;
            }
          }
        }
      }
      
      .supplier-details {
        .detail-row {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 6px;
          font-size: 13px;
          color: #6b7280;
          
          .el-icon {
            color: #9ca3af;
            font-size: 14px;
          }
        }
      }
      
      .view-company-btn-wrapper {
        margin-top: 8px;
      }
    }
    
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
    
    .contact-btn {
      flex: 1;
      height: 42px;
      font-size: 15px;
      font-weight: 600;
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border: none;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
      }
    }
    
    .inquiry-btn {
      flex: 1;
      height: 42px;
      font-size: 15px;
      font-weight: 600;
      border: 2px solid #2563eb;
      color: #2563eb;
      
      &:hover {
        background: #2563eb;
        color: white;
      }
    }
  }
}

.detail-tabs, .product-detail-section {
  .tabs-header {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 20px; // 减少底部间距
    margin-top: 100px;
    
    .tab-item {
      padding: 10px 20px; // 减少内边距
      font-size: 15px;
      font-weight: 600;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        color: #2563eb;
      }
      
      &.active {
        color: #2563eb;
        border-bottom-color: #2563eb;
      }
    }
  }
  
  .tabs-content {
    .tab-panel {
      .rich-content {
        background: white;
        border-radius: 10px;
        padding: 20px; // 减少内边距
        margin-left: 30px;
        border: 1px solid #e5e7eb;
        line-height: 1.5; // 减小行高
        
        :deep(h3) {
          color: #1f2937;
          font-size: 17px; // 减小字体
          font-weight: 600;
          margin: 0 0 10px 0; // 减少底部间距
        }
        
        :deep(p) {
          color: #4b5563;
          margin: 0 0 10px 0; // 减少底部间距
          font-size: 14px;
        }
        
        :deep(ul) {
          margin: 0 0 10px 0; // 减少底部间距
          padding-left: 18px; // 减少左侧缩进
          
          li {
            color: #4b5563;
            margin-bottom: 5px; // 减少底部间距
            font-size: 14px;
          }
        }
      }
      
      .company-images {
        margin-bottom: 20px; // 减少底部间距
        
        .company-gallery {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); // 调整列宽
          gap: 10px; // 减少间距
          
          .company-image {
            width: 100%;
            height: 160px; // 减少高度
            object-fit: cover;
            border-radius: 8px; // 减小圆角
            border: 1px solid #e5e7eb;
          }
        }
      }
      
      .main-markets {
        background: white;
        border-radius: 10px; // 减小圆角
        padding: 20px; // 减少内边距
        border: 1px solid #e5e7eb;
        margin-bottom: 20px; // 减少底部间距
        
        h3 {
          font-size: 17px; // 减小字体
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 12px 0; // 减少底部间距
        }
        
        .markets-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .market-tag {
            background: #eff6ff;
            color: #1e40af;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
      
      .contact-form-section {
        background: white;
        border-radius: 10px; // 减小圆角
        padding: 20px; // 减少内边距
        border: 1px solid #e5e7eb;
        
        h3 {
          font-size: 17px; // 减小字体
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 12px 0; // 减少底部间距
        }
        
        .contact-info {
          display: grid;
          gap: 12px;
          
          .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            color: #1f2937;
            
            .el-icon {
              color: #2563eb;
              font-size: 18px;
            }
          }
        }
      }
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  
  .el-icon {
    font-size: 48px;
    color: #6b7280;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
  }
}

.not-found {
  text-align: center;
  padding: 80px 20px;
  
  .el-icon {
    font-size: 64px;
    color: #d1d5db;
    margin-bottom: 24px;
  }
  
  h2 {
    font-size: 24px;
    color: #1f2937;
    margin: 0 0 16px 0;
  }
  
  p {
    font-size: 16px;
    color: #6b7280;
    margin: 0 0 32px 0;
  }
}

/* 顶层样式（避免嵌套导致的作用范围问题） */
.product-detail-section {
  margin-top: 28px;
}
.product-detail-section .detail-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  margin: 0 0 12px 0;
}
.product-detail-section .section-title {
  margin: 0 !important;
  padding: 0;
  font-size: 22px;
  font-weight: 800;
  color: #111827;
  letter-spacing: 0.3px;
}
.product-detail-section .section-underline {
  width: 72px;
  height: 3px;
  border-radius: 3px;
  background: linear-gradient(90deg, #2563eb, #60a5fa);
}
.product-detail-section .rich-content {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  line-height: 1.7;
}

// 响应式适配
@media (max-width: 1366px) and (min-width: 1025px) {
  .main-content {
    padding-top: 125px; // 主导航65px + 二级导航45px + 间距15px
    
    .container {
      max-width: 1200px;
      padding: 0 24px;
    }
  }
  
  .product-images {
    top: 145px;
    max-height: calc(100vh - 200px);
    overflow: visible;
  }
  
  .product-main .product-layout {
    gap: 24px;
  }
  
  .product-info {
    .product-title {
      font-size: 22px;
      margin-bottom: 14px;
    }
    
    .price-section {
      .price-list .price-item .price-text {
        font-size: 16px;
      }
    }
    
    .action-buttons {
      .contact-btn, .inquiry-btn {
        height: 44px;
        font-size: 15px;
      }
    }
  }
  
  /* 单一产品详情卡片样式（移除公司信息后） */
  .product-detail-section {
    margin-top: 60px;
  }

  .detail-header {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 12px;
  }

  .section-title {
    margin: 0;
    font-size: 22px;
    font-weight: 800;
    color: #111827;
    letter-spacing: 0.3px;
  }

  .section-underline {
    width: 72px;
    height: 3px;
    border-radius: 3px;
    background: linear-gradient(90deg, #2563eb, #60a5fa);
  }

  .product-detail-section .rich-content :deep(h1),
  .product-detail-section .rich-content :deep(h2),
  .product-detail-section .rich-content :deep(h3),
  .product-detail-section .rich-content :deep(h4) {
    color: #111827;
    margin: 0 0 10px 0;
  }
  
  .product-detail-section .rich-content :deep(p) {
    color: #374151;
    margin: 0 0 10px 0;
    font-size: 15px;
  }
  
  .product-detail-section .rich-content :deep(ul),
  .product-detail-section .rich-content :deep(ol) {
    margin: 0 0 12px 18px;
  }
  
  .product-detail-section .rich-content :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
  }

  /* 富文本美化增强 */
  .product-detail-section .rich-content :deep(h1) { font-size: 26px; }
  .product-detail-section .rich-content :deep(h2) { font-size: 22px; }
  .product-detail-section .rich-content :deep(h3) { font-size: 18px; }
  .product-detail-section .rich-content :deep(h4) { font-size: 16px; }
  
  .product-detail-section .rich-content :deep(a) {
    color: #2563eb;
    text-decoration: none;
  }
  .product-detail-section .rich-content :deep(a:hover) { text-decoration: underline; }
  
  .product-detail-section .rich-content :deep(strong) { font-weight: 700; }
  .product-detail-section .rich-content :deep(em) { font-style: italic; }
  
  .product-detail-section .rich-content :deep(blockquote) {
    margin: 10px 0;
    padding: 10px 12px;
    background: #f8fafc;
    border-left: 4px solid #93c5fd;
    color: #334155;
    border-radius: 6px;
  }
  
  .product-detail-section .rich-content :deep(code) {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 2px 6px;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 13px;
  }
  
  .product-detail-section .rich-content :deep(pre) {
    background: #0b1020;
    color: #e5e7eb;
    padding: 12px 14px;
    border-radius: 8px;
    overflow: auto;
    font-size: 13px;
  }
  
  .product-detail-section .rich-content :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
  }
  .product-detail-section .rich-content :deep(th),
  .product-detail-section .rich-content :deep(td) {
    border: 1px solid #e5e7eb;
    padding: 8px 10px;
    text-align: left;
  }
  .product-detail-section .rich-content :deep(th) {
    background: #f1f5f9;
    color: #111827;
  }
  .product-detail-section .rich-content :deep(tr:nth-child(even)) {
    background: #fafafa;
  }
  
  .product-detail-section .rich-content :deep(hr) {
    border: none;
    border-top: 1px solid #e5e7eb;
    margin: 16px 0;
  }
  
  .product-detail-section .rich-content :deep(ul) { list-style: disc; }
  .product-detail-section .rich-content :deep(ol) { list-style: decimal; }
  .product-detail-section .rich-content :deep(li) { margin: 6px 0; }
  
  .product-detail-section .rich-content :deep(iframe),
  .product-detail-section .rich-content :deep(video) {
    max-width: 100%;
    width: 100%;
    border-radius: 8px;
  }
  .product-detail-section .rich-content { padding: 24px; }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-content {
    padding-top: 115px; // 主导航60px + 二级导航42px + 间距13px
    
    .container {
      padding: 0 20px;
    }
  }
  
  .product-images {
    top: 135px;
    max-height: calc(100vh - 190px);
    overflow: visible;
  }
  
  .product-main .product-layout {
    gap: 20px;
  }
  
  .product-info {
    .product-title {
      font-size: 20px;
      margin-bottom: 12px;
    }
    
    .price-section {
      .price-list .price-item .price-text {
        font-size: 15px;
      }
    }
    
    .product-meta {
      gap: 12px;
      
      .meta-item {
        padding: 12px;
      }
    }
    
    .action-buttons {
      gap: 12px;
      
      .contact-btn, .inquiry-btn {
        height: 42px;
        font-size: 14px;
      }
    }
  }
  
  .product-detail-section .rich-content { padding: 20px; }
}

@media (max-width: 968px) {
  .main-content {
    padding-top: 180px; // 移动端头部较高
    
    .container {
      padding: 0 20px;
    }
  }
  
  .product-main .product-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .product-images {
    position: static;
    height: auto;
    max-height: none;
    margin-bottom: 20px;
    overflow: visible;
  }
  
  .product-info {
    .product-title {
      font-size: 24px;
    }
    
    .product-meta {
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .action-buttons {
      flex-direction: column;
    }
  }
  
  .product-detail-section .rich-content { padding: 20px; }
}
</style>

