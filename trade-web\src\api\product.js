import request from './request'

// 获取产品详情
export const getProductDetail = (productId) => {
  return request({
    url: `/v1/public/product/${productId}`,
    method: 'GET'
  })
}

// 搜索产品列表
export const searchProducts = (params) => {
  return request({
    url: '/v1/public/product/search',
    method: 'GET',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      name: params.name, // 商品名称（可选）
      category: params.category, // 一级分类ID（可选）
      sub_category: params.sub_category, // 二级分类ID（可选）
      thd_category: params.thd_category // 三级分类ID（可选）
    }
  })
} 