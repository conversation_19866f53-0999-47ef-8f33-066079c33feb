<template>
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2 class="cta-title">
              {{ currentLang === 'zh' ? '立即开始您的采购之旅' : 'Start Your Sourcing Journey Today' }}
            </h2>
            <p class="cta-subtitle">
              {{ currentLang === 'zh' ? '连接全球优质供应商，发现更多商机' : 'Connect with quality suppliers worldwide and discover more opportunities' }}
            </p>
          </div>
          <div class="cta-actions">
            <el-button 
              type="primary" 
              size="large"
              class="cta-btn"
            >
              {{ currentLang === 'zh' ? '免费注册' : 'Join Free' }}
            </el-button>
            <el-button 
              size="large"
              class="cta-btn-secondary"
            >
              {{ currentLang === 'zh' ? '发布求购' : 'Post Sourcing Request' }}
            </el-button>
          </div>
        </div>
      </div>
    </section>
  </template>
  
  <script>
  import { computed } from 'vue'
  import { useLanguageStore } from '../stores/language.js'
  
  export default {
    name: 'CTASection',
    setup() {
      const languageStore = useLanguageStore()
      const currentLang = computed(() => languageStore.currentLang)
      
      return {
        currentLang
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    
    .cta-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 40px;
    }
    
    .cta-text {
      flex: 1;
      
      .cta-title {
        font-size: 32px;
        font-weight: 700;
        color: white;
        margin: 0 0 16px 0;
        line-height: 1.3;
      }
      
      .cta-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        line-height: 1.5;
      }
    }
    
    .cta-actions {
      display: flex;
      gap: 16px;
      
      .cta-btn {
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        background: white;
        color: var(--primary-color);
        border-color: white;
        
        &:hover {
          background: #f8f9fa;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }
      }
      
      .cta-btn-secondary {
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        background: transparent;
        color: white;
        border: 2px solid white;
        
        &:hover {
          background: white;
          color: var(--primary-color);
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .cta-section {
      padding: 60px 0;
      
      .cta-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
      }
      
      .cta-text {
        .cta-title {
          font-size: 24px;
        }
        
        .cta-subtitle {
          font-size: 16px;
        }
      }
      
      .cta-actions {
        flex-direction: column;
        width: 100%;
        
        .cta-btn,
        .cta-btn-secondary {
          width: 100%;
          padding: 14px 24px;
        }
      }
    }
  }
  </style>