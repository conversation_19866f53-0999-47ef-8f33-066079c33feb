<template>
  <div class="category-sidebar">
    <div class="sidebar-header">
      <el-icon><Menu /></el-icon>
      <h3>{{ currentLang === 'zh' ? '全部分类' : 'All Categories' }}</h3>
    </div>
    <ul class="category-list" v-if="categories && categories.length > 0">
      <li 
        v-for="category in categories" 
        :key="category.id" 
        class="category-item"
        @mouseenter="showSubcategories(category)"
        @mouseleave="hideSubcategories"
      >
        <div class="category-link">
          <span class="category-name">{{ category.name || '' }}</span>
          <el-icon v-if="category.sub_categories && category.sub_categories.length" class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        
        <transition name="slide-fade">
          <div 
            v-if="hoveredCategory === category.id && category.sub_categories && category.sub_categories.length" 
            class="subcategory-panel"
            @mouseenter="keepSubcategoriesOpen"
            @mouseleave="hideSubcategories"
          >
            <div class="subcategory-content">
              <div 
                v-for="subCategory in category.sub_categories" 
                :key="subCategory.id" 
                class="subcategory-column"
              >
                <template v-if="subCategory && subCategory.id">
                  <h4 class="subcategory-title">
                    <div>{{ subCategory.name || '' }}</div>
                  </h4>
                  <ul class="subcategory-list" v-if="subCategory.sub_categories && subCategory.sub_categories.length">
                    <li 
                      v-for="child in subCategory.sub_categories" 
                      :key="child.id"
                    >
                      <template v-if="child && child.id">
                        <div @click="handleThirdCategoryClick(category, subCategory, child)">{{ child.name || '' }}</div>
                      </template>
                    </li>
                  </ul>
                </template>
              </div>
            </div>
          </div>
        </transition>
      </li>
    </ul>
    <div v-else class="empty-categories">
      <p>{{ currentLang === 'zh' ? '暂无分类数据' : 'No categories available' }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Menu, ArrowRight } from '@element-plus/icons-vue'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'CategorySidebar',
  components: { Menu, ArrowRight },
  props: {
    categories: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  setup(props) {
    const router = useRouter()
    const languageStore = useLanguageStore()
    const currentLang = computed(() => languageStore.currentLang)
    
    const hoveredCategory = ref(null)
    let leaveTimer = null

    // 添加调试信息
    console.log('CategorySidebar 接收到的分类数据:', props.categories)

    const showSubcategories = (category) => {
      if (!category || !category.id) return
      
      console.log('显示子分类:', category.name, '子分类数量:', category.sub_categories?.length || 0)
      if (leaveTimer) {
        clearTimeout(leaveTimer)
        leaveTimer = null
      }
      hoveredCategory.value = category.id
    }

    const hideSubcategories = () => {
      console.log('隐藏子分类')
      if (leaveTimer) {
        clearTimeout(leaveTimer)
      }
      leaveTimer = setTimeout(() => {
        hoveredCategory.value = null
        leaveTimer = null
      }, 200)
    }

    const keepSubcategoriesOpen = () => {
      console.log('保持子分类打开')
      if (leaveTimer) {
        clearTimeout(leaveTimer)
        leaveTimer = null
      }
    }
    
    // 处理分类点击 - 只有三级分类（叶子节点）才能点击
    const handleCategoryClick = (category) => {
      // 一级分类不可点击，只用于展开子分类
      console.log('一级分类不可点击:', category?.name || '')
    }
    
    // 处理二级分类点击 - 只用于展开三级分类
    const handleSubCategoryClick = (category, subCategory) => {
      // 二级分类不可点击，只用于展开三级分类
      console.log('二级分类不可点击:', subCategory?.name || '')
    }
    
    // 处理三级分类点击 - 这是唯一可点击的层级
    const handleThirdCategoryClick = (category, subCategory, thirdCategory) => {
      if (!thirdCategory?.id) {
        console.error('分类数据不完整', { category, subCategory, thirdCategory })
        return
      }
      console.log('点击三级分类，跳转列表，分类ID:', thirdCategory.id)
      try {
        router.push({ path: '/products', query: { category_id: thirdCategory.id } })
      } catch (error) {
        console.error('路由跳转失败:', error)
      }
    }
    
    // 组件卸载时清理定时器
    onUnmounted(() => {
      if (leaveTimer) {
        clearTimeout(leaveTimer)
        leaveTimer = null
      }
    })
    
    return {
      currentLang,
      hoveredCategory,
      showSubcategories,
      hideSubcategories,
      keepSubcategoriesOpen,
      handleCategoryClick,
      handleSubCategoryClick,
      handleThirdCategoryClick
    }
  }
}
</script>

<style lang="scss" scoped>
.category-sidebar {
  width: 260px;
  height: 480px; /* 调整高度与轮播图一致 */
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
  overflow: visible; /* 修改为visible以便子目录可以显示在外部 */
  display: flex;
  flex-direction: column;
  
  .sidebar-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: #f1f5f9;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    color: #1e3a8a;
    
    .el-icon {
      margin-right: 10px;
      font-size: 18px;
    }
  }
  
  .category-list {
    padding: 5px 0;
    flex: 1;
    overflow: visible;
    
    /* 隐藏滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    
    .category-item {
      position: relative;
      padding: 10px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f0f7ff;
        color: #2563eb;
      }
      
      .category-link {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: inherit;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }
      
      .category-name {
        flex: 1;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .arrow-icon {
        font-size: 12px;
        margin-left: 8px;
      }
    }
  }
  
  .subcategory-panel {
    position: absolute;
    top: 0;
    left: 100%;
    width: 500px;
    min-height: 100%;
    background: white;
    box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 0 8px 8px 0;
    padding: 15px 20px;
    z-index: 1001;
    border: 1px solid #e5e7eb;
    
    .subcategory-content {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .subcategory-column {
        width: calc(50% - 10px);
        
        .subcategory-title {
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 10px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e5e7eb;
          color: #2563eb;
          cursor: pointer;
          
          div {
            transition: color 0.3s ease;
            
            &:hover {
              color: #1d4ed8;
            }
          }
        }
        
        .subcategory-list {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            padding: 5px 0;
            cursor: pointer;
            font-size: 14px;
            transition: color 0.3s ease;
            
            div {
              cursor: pointer;
              transition: color 0.3s ease;
              
              &:hover {
                color: #2563eb;
              }
            }
          }
        }
      }
    }
  }
}

.empty-categories {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-size: 14px;
  
  p {
    margin: 0;
  }
}

// 动画过渡效果
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

@media (max-width: 968px) {
  .category-sidebar {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
    
    .subcategory-panel {
      position: static;
      width: 100%;
      box-shadow: none;
      padding: 5px 20px 15px;
      
      .subcategory-content {
        .subcategory-column {
          width: 100%;
        }
      }
    }
  }
}
</style> 