<template>
  <div class="product-section">
    <div class="container">
      <div 
        v-for="section in hotCategories" 
        :key="section.id"
        class="category-section"
      >
        <div class="category-layout">
          <!-- Left: Category Info -->
          <div class="category-info" :style="{ backgroundImage: `url(${section.logo || defaultImage})` }">
            <div class="category-overlay">
              <div class="category-content">
                <h3 class="category-title">
                  {{ currentLang === 'zh' ? section.name : (section.name_en || section.name) }}
                </h3>
                <p class="category-description">
                  {{ currentLang === 'zh' ? section.description : (section.description_en || section.description) }}
                </p>
                <el-button 
                  type="primary" 
                  @click="viewAllProducts(section)"
                  class="view-all-btn"
                >
                  {{ currentLang === 'zh' ? '查看全部' : 'View All' }}
                </el-button>
              </div>
            </div>
          </div>

          <!-- Right: Product Display (Sub-categories) -->
          <div class="products-display">
            <div class="products-grid">
              <div 
                v-for="product in section.sub_categories?.slice(0, 8)" 
                :key="product.id"
                class="product-card"
                @click="viewProduct(product)"
              >
                <div class="product-image">
                  <img :src="product.logo || defaultProductImage" :alt="currentLang === 'zh' ? product.name : (product.name_en || product.name)" />
                  <div class="product-overlay">
                    <el-button size="small" type="primary" class="quick-view">
                      {{ currentLang === 'zh' ? '快速查看' : 'Quick View' }}
                    </el-button>
                  </div>
                </div>
                
                <div class="product-info">
                  <h4 class="product-name">
                    {{ currentLang === 'zh' ? product.name : (product.name_en || product.name) }}
                  </h4>
                  <p class="product-price">{{ '价格面议' }}</p>
                  <p class="product-supplier">{{ section.name }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'ProductSection',
  props: {
    hotCategories: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  setup(props) {
    const router = useRouter()
    const languageStore = useLanguageStore()
    const currentLang = computed(() => languageStore.currentLang)
    
    const defaultImage = 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'
    const defaultProductImage = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    
    const viewProduct = (product) => {
      console.log('查看分类下的产品列表，分类ID:', product?.id)
      if (!product || !product.id) return
      router.push({
        path: '/products',
        query: { category_id: product.id }
      })
    }
    
    const viewAllProducts = (section) => {
      console.log('查看分类所有产品，分类ID:', section?.id)
      if (!section || !section.id) return
      router.push({
        path: '/products',
        query: { category_id: section.id }
      })
    }
    
    return {
      currentLang,
      defaultImage,
      defaultProductImage,
      viewProduct,
      viewAllProducts
    }
  }
}
</script>

<style lang="scss" scoped>
.product-section {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .category-section {
    margin-bottom: 80px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .category-layout {
    display: flex;
    gap: 40px;
    align-items: flex-start;
    
    .category-info {
      flex: 0 0 350px;
      height: 376px;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 20px;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      overflow: hidden;
      position: relative;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }
      
      .category-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(37, 99, 235, 0.9) 0%, rgba(29, 78, 216, 0.8) 100%);
        padding: 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: white;
      }
      
      .category-title {
        font-size: 28px;
        font-weight: 700;
        color: white;
        margin: 0 0 16px 0;
        line-height: 1.3;
        text-shadow: 2px 4px 8px rgba(0, 0, 0, 0.3);
      }
      
      .category-description {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
        margin: 0 0 32px 0;
        text-shadow: 1px 2px 4px rgba(0, 0, 0, 0.3);
      }
      
      .view-all-btn {
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        
        &:hover {
          background: white;
          color: #2563eb;
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(255, 255, 255, 0.4);
        }
      }
    }
    
    .products-display {
      flex: 1;
      min-width: 0;
      height: 376px;
    }
    
    .products-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 20px;
      height: 100%;
    }
    
    .product-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        
        .product-overlay {
          opacity: 1;
        }
      }
      
      .product-image {
        position: relative;
        height: 140px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        &:hover img {
          transform: scale(1.1);
        }
        
        .product-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(37, 99, 235, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          
          .quick-view {
            border: 2px solid white;
            background: transparent;
            color: white;
            font-weight: 600;
            font-size: 12px;
            padding: 6px 12px;
            
            &:hover {
              background: white;
              color: #2563eb;
            }
          }
        }
      }
      
      .product-info {
        padding: 16px;
        
        .product-name {
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
          line-height: 1.4;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        .product-price {
          font-size: 16px;
          font-weight: 700;
          color: #2563eb;
          margin: 0 0 6px 0;
        }
        
        .product-supplier {
          font-size: 12px;
          color: #64748b;
          margin: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .product-section {
    .category-layout {
      flex-direction: column;
      gap: 30px;
      
      .category-info {
        flex: none;
        width: 100%;
      }
      
      .products-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .product-section {
    padding: 40px 0;
    
    .category-section {
      margin-bottom: 50px;
    }
    
    .category-layout {
      .category-info {
        padding: 30px;
        
        .category-title {
          font-size: 24px;
        }
      }
      
      .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
      }
    }
  }
}

@media (max-width: 480px) {
  .product-section {
    .category-layout {
      .products-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .product-card {
        .product-image {
          height: 120px;
        }
        
        .product-info {
          padding: 12px;
          
          .product-name {
            font-size: 13px;
          }
          
          .product-price {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style> 