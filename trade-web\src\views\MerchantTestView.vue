<template>
  <div class="merchant-test">
    <div class="container">
      <h1>商户API测试页面</h1>
      
      <div class="test-controls">
        <el-input 
          v-model="merchantId" 
          placeholder="请输入商户ID" 
          style="width: 200px; margin-right: 16px;"
        />
        <el-button type="primary" @click="testAPI" :loading="loading">
          测试API
        </el-button>
      </div>
      
      <div v-if="loading" class="loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载...</span>
      </div>
      
      <div v-if="error" class="error">
        <el-alert type="error" :title="error" show-icon />
      </div>
      
      <div v-if="merchantData" class="result">
        <h2>API返回数据：</h2>
        <pre>{{ JSON.stringify(rawData, null, 2) }}</pre>
        
        <h2>解析后的数据：</h2>
        <div class="parsed-data">
          <div class="data-section">
            <h3>基础信息</h3>
            <p><strong>企业名称:</strong> {{ merchantData.name }}</p>
            <p><strong>Logo:</strong> <img v-if="merchantData.logo" :src="merchantData.logo" alt="Logo" style="width: 50px; height: 50px;" /></p>
            <p><strong>简介:</strong> {{ merchantData.description }}</p>
          </div>
          
          <div class="data-section">
            <h3>企业档案</h3>
            <p><strong>企业类型:</strong> {{ merchantData.businessType }}</p>
            <p><strong>员工数量:</strong> {{ merchantData.employeeCount }}</p>
            <p><strong>成立时间:</strong> {{ merchantData.establishYear }}</p>
            <p><strong>注册资本:</strong> {{ merchantData.registeredCapital }} {{ merchantData.registeredCapitalCurrency }}</p>
          </div>
          
          <div class="data-section">
            <h3>联系方式</h3>
            <p><strong>地址:</strong> {{ merchantData.address }}</p>
            <p><strong>电话:</strong> {{ merchantData.telephone }}</p>
            <p><strong>官网:</strong> {{ merchantData.website }}</p>
          </div>
          
          <div class="data-section" v-if="merchantData.banners.length > 0">
            <h3>轮播图</h3>
            <div class="banners">
              <img 
                v-for="banner in merchantData.banners" 
                :key="banner.id"
                :src="banner.image" 
                :alt="banner.title"
                style="width: 100px; height: 60px; margin-right: 8px; object-fit: cover;"
              />
            </div>
          </div>
        </div>
        
        <div class="actions">
          <el-button type="success" @click="goToCompanyPage">
            前往商户主页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Loading } from '@element-plus/icons-vue'
import { getMerchantDetail, formatMerchantData } from '../api/merchant.js'
import { ElMessage } from 'element-plus'

const router = useRouter()

const merchantId = ref('1')
const loading = ref(false)
const error = ref(null)
const rawData = ref(null)
const merchantData = ref(null)

const testAPI = async () => {
  if (!merchantId.value) {
    ElMessage.warning('请输入商户ID')
    return
  }

  try {
    loading.value = true
    error.value = null
    rawData.value = null
    merchantData.value = null
    
    const response = await getMerchantDetail(merchantId.value)
    rawData.value = response
    
    if (response.code === 0) {
      merchantData.value = formatMerchantData(response)
      ElMessage.success('API调用成功')
    } else {
      error.value = response.msg || 'API返回错误'
    }
  } catch (err) {
    error.value = '网络请求失败: ' + err.message
    console.error('API测试失败:', err)
  } finally {
    loading.value = false
  }
}

const goToCompanyPage = () => {
  router.push(`/company/${merchantId.value}`)
}
</script>

<style lang="scss" scoped>
.merchant-test {
  padding: 40px 20px;
  min-height: 100vh;
  background: #f5f5f5;
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  h1 {
    color: #1f2937;
    margin-bottom: 30px;
  }
  
  .test-controls {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
  }
  
  .loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2563eb;
    margin: 20px 0;
  }
  
  .error {
    margin: 20px 0;
  }
  
  .result {
    margin-top: 30px;
    
    h2 {
      color: #1f2937;
      margin: 20px 0 10px 0;
    }
    
    pre {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 6px;
      overflow-x: auto;
      font-size: 12px;
      max-height: 300px;
    }
  }
  
  .parsed-data {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
    
    .data-section {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 6px;
      
      h3 {
        color: #2563eb;
        margin: 0 0 12px 0;
        font-size: 16px;
      }
      
      p {
        margin: 8px 0;
        font-size: 14px;
        
        strong {
          color: #1f2937;
        }
      }
    }
  }
  
  .banners {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .actions {
    margin-top: 30px;
    text-align: center;
  }
}
</style>
