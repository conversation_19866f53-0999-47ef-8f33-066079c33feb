<template>
    <div class="custom-search">
      <div class="search-wrapper">
        <!-- 搜索类型选择 -->
        <div class="search-type-selector">
          <select v-model="searchType" class="type-select">
            <option value="products">{{ currentLang === 'zh' ? '产品' : 'Products' }}</option>
            <option value="suppliers">{{ currentLang === 'zh' ? '供应商' : 'Suppliers' }}</option>
          </select>
          <div class="select-arrow">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M7 10l5 5 5-5z" fill="currentColor"/>
            </svg>
          </div>
        </div>
        
        <!-- 搜索输入框 -->
        <div class="search-input-wrapper">
          <input 
            v-model="searchQuery"
            type="text"
            :placeholder="currentLang === 'zh' ? '搜索产品或供应商...' : 'Search products or suppliers...'"
            class="search-input"
            @keyup.enter="handleSearch"
          >
          <div class="search-icon" @click="handleSearch">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" fill="currentColor"/>
            </svg>
          </div>
        </div>
        
        <!-- 搜索按钮 -->
        <button class="search-button" @click="handleSearch">
          <span>{{ currentLang === 'zh' ? '搜索' : 'Search' }}</span>
        </button>
      </div>
    </div>
  </template>
  
  <script>
  import { ref, computed } from 'vue'
  import { useLanguageStore } from '../stores/language.js'
  
  export default {
    name: 'CustomSearch',
    setup() {
      const languageStore = useLanguageStore()
      const searchQuery = ref('')
      const searchType = ref('products')
      
      const currentLang = computed(() => languageStore.currentLang)
      
      const handleSearch = () => {
        if (searchQuery.value.trim()) {
          console.log('搜索:', searchQuery.value, '类型:', searchType.value)
          // 这里可以添加搜索逻辑
        }
      }
      
      return {
        searchQuery,
        searchType,
        currentLang,
        handleSearch
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .custom-search {
    position: relative;
    width: 100%;
    max-width: 800px;
    
    .search-wrapper {
      display: flex;
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      border: 2px solid transparent;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        border-color: #2563eb;
        box-shadow: 0 12px 40px rgba(37, 99, 235, 0.15);
        transform: translateY(-1px);
      }
      
      &:focus-within {
        border-color: #2563eb;
        box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), 0 12px 40px rgba(37, 99, 235, 0.2);
        transform: translateY(-2px);
      }
    }
    
    .search-type-selector {
      position: relative;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 0 24px;
      display: flex;
      align-items: center;
      min-width: 150px;
      border-right: 1px solid #e8eaed;
      
      .type-select {
        appearance: none;
        background: transparent;
        border: none;
        outline: none;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        cursor: pointer;
        padding-right: 28px;
        width: 100%;
      }
      
      .select-arrow {
        position: absolute;
        right: 12px;
        color: #6b7280;
        pointer-events: none;
        transition: all 0.3s ease;
      }
      
      &:hover {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        
        .select-arrow {
          transform: translateY(-1px);
          color: #2563eb;
        }
        
        .type-select {
          color: #2563eb;
        }
      }
    }
    
    .search-input-wrapper {
      flex: 1;
      display: flex;
      align-items: center;
      position: relative;
      padding: 0 20px;
      
      .search-input {
        width: 100%;
        border: none;
        outline: none;
        font-size: 18px;
        font-weight: 400;
        color: #1f2937;
        background: transparent;
        padding: 15px 0;
        
        &::placeholder {
          color: #9ca3af;
          font-weight: 400;
        }
        
        &:focus::placeholder {
          opacity: 0.6;
        }
      }
      
      .search-icon {
        color: #9ca3af;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 6px;
        border-radius: 6px;
        
        &:hover {
          color: #2563eb;
          background: rgba(37, 99, 235, 0.1);
          transform: scale(1.1);
        }
      }
    }
    
    .search-button {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: white;
      border: none;
      padding: 0 36px;
      font-size: 18px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s;
      }
      
      &:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: scale(1.05);
        box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.1);
        
        &::before {
          left: 100%;
        }
      }
      
      &:active {
        transform: scale(1.02);
      }
      
      span {
        position: relative;
        z-index: 1;
      }
    }
  }
  
  @media (max-width: 768px) {
    .custom-search {
      max-width: 100%;
      
      .search-wrapper {
        flex-direction: column;
        border-radius: 12px;
      }
      
      .search-type-selector {
        min-width: auto;
        border-right: none;
        border-bottom: 1px solid #e8eaed;
        padding: 16px 20px;
        justify-content: center;
      }
      
      .search-input-wrapper {
        padding: 0 20px;
        
        .search-input {
          padding: 18px 0;
          font-size: 16px;
        }
      }
      
      .search-button {
        padding: 18px 24px;
        font-size: 16px;
        border-radius: 0;
      }
    }
  }
  </style>