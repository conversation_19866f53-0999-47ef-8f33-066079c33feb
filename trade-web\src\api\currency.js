import request from './request'

/**
 * 获取所有货币列表
 */
export const getAllCurrencies = () => {
  return request({
    url: '/v1/web/currency',
    method: 'get'
  })
}

/**
 * 根据货币代码获取货币信息
 * @param {string} code - 货币代码
 */
export const getCurrencyByCode = async (code) => {
  const currencies = await getAllCurrencies()
  return currencies.find(currency => currency.code === code)
}

// 常用货币列表
export const popularCurrencies = [
  'USD', 'EUR', 'CNY', 'JPY', 'GBP', 'HKD'
]

// 获取常用货币列表
export const getPopularCurrencies = async () => {
  const allCurrencies = await getAllCurrencies()
  return allCurrencies.filter(currency => 
    popularCurrencies.includes(currency.code)
  )
}
