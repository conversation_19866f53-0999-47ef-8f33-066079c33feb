import './assets/main.css'
import './assets/global.scss'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

// 全局样式 - 隐藏滚动条但保持滚动功能
const globalStyles = `
  /* 重置样式和布局修复 */
  * {
    box-sizing: border-box;
  }
  
  html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow-x: hidden;
  }
  
  #app {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
  }
  
  /* 隐藏滚动条但保持功能 */
  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
  
  * {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  /* 现代化CSS变量 */
  :root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #f8fafc;
    --text-color: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e8eaed;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --border-radius-large: 16px;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    line-height: 1.6;
    color: var(--text-color);
  }
  
  .container {
    max-width: 1600px !important;
    margin: 0 auto;
    padding: 0 24px;
  }
  
  /* 特殊容器用于更宽的布局 */
  .wide-container {
    max-width: 1800px !important;
    margin: 0 auto;
    padding: 0 30px;
  }
  
  /* 移动端头部遮挡修复 */
  @media (max-width: 968px) {
    .main-content {
      padding-top: 220px !important;
    }
  }
  
  @media (max-width: 480px) {
    .main-content {
      padding-top: 200px !important;
    }
  }
`

// 创建并注入样式
const styleSheet = document.createElement('style')
styleSheet.textContent = globalStyles
document.head.appendChild(styleSheet)

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
