import request from './request'

// 搜索商户列表
export const searchMerchants = (params = {}) => {
  return request({
    url: '/v1/public/merchant/search',
    method: 'GET',
    params: {
      page: params.page || 1,
      size: params.size || 20,
      name: params.name
    }
  })
}


/**
 * 获取商户详情
 * @param {number} merchantId - 商户ID
 * @returns {Promise} 商户详情数据
 */
export const getMerchantDetail = (merchantId) => {
  return request({
    url: `/v1/public/merchant/${merchantId}`,
    method: 'GET'
  })
}

/**
 * 解析商户属性数据
 * @param {Array} attributes - 商户属性数组
 * @returns {Object} 解析后的商户数据
 */
export const parseMerchantAttributes = (attributes) => {
  const merchantData = {
    // 基础信息
    name: '',
    logo: '',
    mainImage: '',
    description: '',
    banners: [],
    
    // 企业档案
    businessType: '',
    mainProducts: '',
    employeeCount: '',
    establishYear: '',
    registeredCapital: '',
    registeredCapitalCurrency: '',
    profile: '',
    
    // 联系方式
    address: '',
    telephone: '',
    website: '',
    homepage: '',
    latitude: '',
    longitude: '',
    contactName: '', // 新增：联系人名字
    qrCode: '' // 新增：微信二维码
  }

  // 遍历属性数组，根据attribute.name映射数据
  attributes.forEach(attr => {
    const attributeName = attr.attribute.name
    const value = attr.value
    const valueType = attr.attribute.value_type
    const isImage = attr.attribute.is_image
    const isArray = valueType === 'array'

    switch (attributeName) {
      // 基础信息 (type: "base")
      case '企业名称':
        merchantData.name = value
        break
      case 'Logo':
        merchantData.logo = value
        break
      case '主页图片':
        merchantData.mainImage = value
        break
      case '简介':
        merchantData.description = value
        break
      case '轮播图':
        // 轮播图是JSON数组格式，需要解析
        try {
          if (isArray && value) {
            const bannerUrls = JSON.parse(value)
            merchantData.banners = bannerUrls.map((url, index) => ({
              id: index + 1,
              image: url,
              title: `轮播图 ${index + 1}`,
              titleEn: `Banner ${index + 1}`,
              description: '',
              descriptionEn: ''
            }))
          }
        } catch (error) {
          console.warn('解析轮播图数据失败:', error)
          merchantData.banners = []
        }
        break

      // 企业档案 (type: "profile")
      case 'BusinessType':
        merchantData.businessType = value
        break
      case 'MainProducts':
        merchantData.mainProducts = value
        break
      case 'Number of Employees':
        merchantData.employeeCount = value
        break
      case 'Year of Establishment':
        merchantData.establishYear = value
        break
      case 'Registered Capital':
        merchantData.registeredCapital = value
        break
      case 'Registered Capital Currency':
        merchantData.registeredCapitalCurrency = value
        break
      case 'Profile':
        merchantData.profile = value
        break

      // 联系方式 (type: "contact")
      case 'Adddress': // 注意：API中是"Adddress"（三个d）
        merchantData.address = value
        break
      case 'Telohone': // 注意：API中是"Telohone"（拼写错误）
        merchantData.telephone = value
        break
      case 'Website':
        merchantData.website = value
        break
      case 'HomePage':
        merchantData.homepage = value
        break
      case 'Lat':
        merchantData.latitude = value
        break
      case 'Lng':
        merchantData.longitude = value
        break
      case 'Contact Name':
        merchantData.contactName = value
        break
      case 'QrCode':
        merchantData.qrCode = value
        break
    }
  })

  return merchantData
}

/**
 * 格式化商户数据为组件所需格式
 * @param {Object} rawData - API返回的原始数据
 * @returns {Object} 格式化后的商户数据
 */
export const formatMerchantData = (rawData) => {
  if (!rawData || !rawData.data) {
    return null
  }

  const { id, name, attributes } = rawData.data
  const parsedData = parseMerchantAttributes(attributes)

  // 格式化为组件所需的数据结构
  return {
    id,
    name: parsedData.name || name, // 优先使用属性中的企业名称
    nameEn: parsedData.name || name, // 暂时使用中文名称，后续可支持多语言
    logo: parsedData.logo || '/src/assets/logo.png', // 默认Logo
    rating: 4.8, // 默认评级，后续可从API获取
    description: parsedData.description,
    descriptionEn: parsedData.description,
    
    // 轮播图数据
    banners: parsedData.banners.length > 0 ? parsedData.banners : [
      {
        id: 1,
        image: parsedData.mainImage || '/src/assets/logo.png',
        title: parsedData.name || '企业展示',
        titleEn: parsedData.name || 'Company Showcase',
        description: parsedData.description || '',
        descriptionEn: parsedData.description || ''
      }
    ],
    
    // 企业档案信息
    businessType: parsedData.businessType,
    mainProducts: parsedData.mainProducts,
    employeeCount: parsedData.employeeCount,
    establishYear: parsedData.establishYear,
    registeredCapital: parsedData.registeredCapital,
    registeredCapitalCurrency: parsedData.registeredCapitalCurrency,
    profile: parsedData.profile,
    
    // 联系信息
    address: parsedData.address,
    telephone: parsedData.telephone,
    website: parsedData.website,
    homepage: parsedData.homepage,
    latitude: parsedData.latitude,
    longitude: parsedData.longitude,
    contactName: parsedData.contactName, // 新增：联系人名字
    qrCode: parsedData.qrCode, // 新增：微信二维码
    
    // 兼容现有组件的字段
    factorySize: '5000m²', // 默认值，后续可从API获取
    exportMarkets: '120+' // 默认值，后续可从API获取
  }
}

/**
 * 获取商户分类树
 * @param {number} merchantId - 商户ID
 * @returns {Promise}
 */
export const getMerchantCategoryTree = (merchantId) => {
  return request({
    url: '/v1/public/merchant/category/tree',
    method: 'GET',
    params: {
      merchant_id: merchantId
    }
  })
}

/**
 * 搜索商户产品列表
 * @param {object} params - 查询参数
 * @param {number} params.merchant_id - 商户ID (必需)
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 * @param {string} params.name - 商品名称
 * @param {number} params.merchant_category_id - 商品目录ID
 * @returns {Promise}
 */
export const searchMerchantProducts = (params) => {
  return request({
    url: '/v1/public/merchant/product/search',
    method: 'GET',
    params
  })
}
