<template>
  <div class="contact-supplier">
    <!-- 固定头部 -->
    <HeaderNav />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 面包屑导航
        <div class="breadcrumb">
          <span @click="$router.push('/')" class="breadcrumb-item">
            {{ currentLang === 'zh' ? '首页' : 'Home' }}
          </span>
          <span class="separator">/</span>
          <span class="breadcrumb-item active">
            {{ currentLang === 'zh' ? '联系供应商' : 'Contact Supplier' }}
          </span>
        </div> -->
        
        <!-- 联系表单 -->
        <div class="contact-form-container">
          <div class="form-card">
            <h2 class="form-title">
              {{ currentLang === 'zh' ? '发送消息给此供应商' : 'Send your message to this supplier' }}
            </h2>
            
            <el-form 
              ref="contactFormRef"
              :model="contactForm"
              :rules="formRules"
              label-position="top"
              class="contact-form"
            >
              <!-- From 字段 -->
              <el-form-item :label="currentLang === 'zh' ? '发件人：' : 'From:'" prop="email" class="form-item">
                <el-input
                  v-model="contactForm.email"
                  :placeholder="currentLang === 'zh' ? '请输入您的邮箱地址' : 'Enter your email address'"
                  class="email-input"
                  size="large"
                />
              </el-form-item>
              
              <!-- To 字段 -->
              <el-form-item :label="currentLang === 'zh' ? '收件人：' : 'To:'" class="form-item">
                <div class="recipient-info">
                  <el-avatar 
                    :src="supplier?.avatar || '/api/placeholder/40/40'" 
                    :size="40"
                    class="recipient-avatar"
                  />
                  <span class="recipient-name">
                    {{ supplier?.contactPerson || 'Ms. Susan' }}
                  </span>
                </div>
              </el-form-item>
              
              <!-- Message 字段 -->
              <el-form-item :label="currentLang === 'zh' ? '消息内容：' : 'Message:'" prop="message" class="form-item">
                <el-input
                  v-model="contactForm.message"
                  type="textarea"
                  :rows="8"
                  :placeholder="currentLang === 'zh' ? '建议您在此详细说明产品需求和公司信息。' : 'We suggest you detail your product requirements and company information here.'"
                  class="message-input"
                  @input="updateCharCount"
                />
                <div class="char-count">
                  <span :class="{ 'error': charCount < 20 || charCount > 4000 }">
                    {{ currentLang === 'zh' ? '请输入20到4000个字符。' : 'Enter between 20 to 4,000 characters.' }}
                  </span>
                  <span class="count">{{ charCount }}/4000</span>
                </div>
              </el-form-item>
              
              <!-- 操作按钮区域 -->
              <div class="action-area">
                <el-button 
                  type="primary" 
                  @click="sendMessage"
                  :loading="sending"
                  :disabled="charCount < 20 || charCount > 4000"
                  class="send-btn"
                  size="large"
                >
                  {{ currentLang === 'zh' ? '发送' : 'Send' }}
                </el-button>
                
                <div class="additional-options">
                  <span class="option-text">
                    {{ currentLang === 'zh' ? '这不是您要找的？' : 'This is not what you are looking for?' }}
                  </span>
                  <el-button 
                    type="text" 
                    @click="postSourcingRequest"
                    class="sourcing-btn"
                  >
                    {{ currentLang === 'zh' ? '立即发布采购需求' : 'Post a Sourcing Request Now' }}
                  </el-button>
                </div>
              </div>
            </el-form>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>{{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}</p>
        </div>
        
        <!-- 供应商未找到 -->
        <div v-if="!loading && !supplier" class="not-found">
          <el-icon><DocumentDelete /></el-icon>
          <h2>{{ currentLang === 'zh' ? '供应商未找到' : 'Supplier Not Found' }}</h2>
          <p>{{ currentLang === 'zh' ? '抱歉，您访问的供应商不存在。' : 'Sorry, the supplier you are looking for does not exist.' }}</p>
          <el-button @click="$router.push('/')" type="primary">
            {{ currentLang === 'zh' ? '返回首页' : 'Back to Home' }}
          </el-button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading, DocumentDelete } from '@element-plus/icons-vue'
import { useLanguageStore } from '../stores/language.js'
import { companies } from '../data/mockData.js'
import HeaderNav from '../components/HeaderNav.vue'

export default {
  name: 'ContactSupplierView',
  components: {
    HeaderNav
  },
  props: {
    supplierId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const languageStore = useLanguageStore()
    
    const loading = ref(true)
    const sending = ref(false)
    const contactFormRef = ref()
    const charCount = ref(0)
    
    const currentLang = computed(() => languageStore.currentLang)
    
    // 联系表单数据
    const contactForm = ref({
      email: '',
      message: ''
    })
    
    // 表单验证规则
    const formRules = computed(() => ({
      email: [
        { 
          required: true, 
          message: currentLang.value === 'zh' ? '请输入您的邮箱地址' : 'Please enter your email address', 
          trigger: 'blur' 
        },
        { 
          type: 'email', 
          message: currentLang.value === 'zh' ? '请输入有效的邮箱地址' : 'Please enter a valid email address', 
          trigger: 'blur' 
        }
      ],
      message: [
        { 
          required: true, 
          message: currentLang.value === 'zh' ? '请输入消息内容' : 'Please enter your message', 
          trigger: 'blur' 
        },
        { 
          min: 20, 
          max: 4000, 
          message: currentLang.value === 'zh' ? '消息内容应在20到4000个字符之间' : 'Message should be between 20 to 4,000 characters', 
          trigger: 'blur' 
        }
      ]
    }))
    
    // 获取供应商信息
    const supplier = computed(() => {
      return companies.find(company => company.id === parseInt(props.supplierId))
    })
    
    // 更新字符计数
    const updateCharCount = () => {
      charCount.value = contactForm.value.message.length
    }
    
    // 发送消息
    const sendMessage = async () => {
      try {
        const valid = await contactFormRef.value.validate()
        if (!valid) return
        
        if (charCount.value < 20 || charCount.value > 4000) {
          ElMessage.error(currentLang.value === 'zh' ? '消息内容应在20到4000个字符之间' : 'Message should be between 20 to 4,000 characters')
          return
        }
        
        sending.value = true
        
        // 模拟发送请求
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        ElMessage.success(currentLang.value === 'zh' ? '消息发送成功！' : 'Message sent successfully!')
        
        // 重置表单
        contactForm.value = {
          email: '',
          message: ''
        }
        charCount.value = 0
        
      } catch (error) {
        console.error('Send message error:', error)
        ElMessage.error(currentLang.value === 'zh' ? '发送失败，请重试。' : 'Failed to send message. Please try again.')
      } finally {
        sending.value = false
      }
    }
    
    // 发布采购请求
    const postSourcingRequest = () => {
      ElMessageBox.confirm(
        currentLang.value === 'zh' 
          ? '这将跳转到发布采购需求页面。是否继续？' 
          : 'This will redirect you to post a sourcing request. Continue?',
        currentLang.value === 'zh' ? '发布采购需求' : 'Post Sourcing Request',
        {
          confirmButtonText: currentLang.value === 'zh' ? '继续' : 'Continue',
          cancelButtonText: currentLang.value === 'zh' ? '取消' : 'Cancel',
          type: 'info',
        }
      ).then(() => {
        ElMessage.info(currentLang.value === 'zh' ? '正在跳转到采购需求页面...' : 'Redirecting to sourcing request page...')
        // 这里可以跳转到采购请求页面
      }).catch(() => {
        // 用户取消
      })
    }
    
    // 初始化
    onMounted(() => {
      languageStore.initLanguage()
      // 模拟加载延迟
      setTimeout(() => {
        loading.value = false
      }, 800)
    })
    
    // 监听路由变化
    watch(() => route.params.supplierId, () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
      }, 500)
    })
    
    return {
      loading,
      sending,
      contactFormRef,
      charCount,
      currentLang,
      supplier,
      contactForm,
      formRules,
      updateCharCount,
      sendMessage,
      postSourcingRequest,
      Loading,
      DocumentDelete
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-supplier {
  min-height: 100vh;
  background: #f8fafc;
}

.main-content {
  padding-top: 140px; // 调整以适应新的头部高度 (主导航70px + 二级导航50px + 间距20px)
  padding-bottom: 48px;
  
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 30px;
  }
}

.breadcrumb {
  margin-bottom: 32px;
  font-size: 14px;
  
  .breadcrumb-item {
    color: #6b7280;
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: #2563eb;
    }
    
    &.active {
      color: #1f2937;
      font-weight: 500;
      cursor: default;
    }
  }
  
  .separator {
    margin: 0 8px;
    color: #d1d5db;
  }
}

.contact-form-container {
  .form-card {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }
  
  .form-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 32px 0;
  }
}

.contact-form {
  .form-item {
    margin-bottom: 24px;
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #374151;
      padding-bottom: 8px;
      
      &::after {
        content: '*';
        color: #ef4444;
        margin-left: 4px;
      }
    }
    
    &:nth-child(2) :deep(.el-form-item__label)::after {
      display: none; // To字段不需要必填标记
    }
  }
  
  .email-input {
    :deep(.el-input__wrapper) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      
      &:hover {
        border-color: #9ca3af;
      }
      
      &.is-focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }
    }
  }
  
  .recipient-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    
    .recipient-avatar {
      flex-shrink: 0;
    }
    
    .recipient-name {
      font-weight: 500;
      color: #1f2937;
    }
  }
  
  .message-input {
    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      resize: vertical;
      min-height: 120px;
      
      &:hover {
        border-color: #9ca3af;
      }
      
      &:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }
    }
  }
  
  .char-count {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #6b7280;
    
    .error {
      color: #ef4444;
    }
    
    .count {
      font-weight: 500;
    }
  }
}

.action-area {
  margin-top: 32px;
  
  .send-btn {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    padding: 12px 32px;
    font-weight: 600;
    border-radius: 6px;
    
    &:hover {
      background: #dc2626;
      border-color: #dc2626;
    }
    
    &:disabled {
      background: #f3f4f6;
      border-color: #e5e7eb;
      color: #9ca3af;
    }
  }
  
  .additional-options {
    margin-top: 16px;
    text-align: left;
    
    .option-text {
      font-size: 14px;
      color: #6b7280;
      margin-right: 8px;
    }
    
    .sourcing-btn {
      color: #2563eb;
      padding: 0;
      height: auto;
      font-size: 14px;
      text-decoration: underline;
      
      &:hover {
        color: #1d4ed8;
      }
    }
  }
}

.loading-state {
  text-align: center;
  padding: 80px 20px;
  
  .el-icon {
    font-size: 32px;
    color: #2563eb;
    margin-bottom: 16px;
  }
  
  p {
    color: #6b7280;
    margin: 0;
  }
}

.not-found {
  text-align: center;
  padding: 80px 20px;
  
  .el-icon {
    font-size: 64px;
    color: #d1d5db;
    margin-bottom: 24px;
  }
  
  h2 {
    font-size: 24px;
    color: #1f2937;
    margin: 0 0 16px 0;
  }
  
  p {
    font-size: 16px;
    color: #6b7280;
    margin: 0 0 32px 0;
  }
}

// 响应式适配
@media (max-width: 1366px) and (min-width: 1025px) {
  .main-content {
    padding-top: 125px; // 主导航65px + 二级导航45px + 间距15px
    
    .container {
      max-width: 750px;
      padding: 0 24px;
    }
  }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-content {
    padding-top: 115px; // 主导航60px + 二级导航42px + 间距13px
    
    .container {
      max-width: 700px;
      padding: 0 20px;
    }
  }
  
  .contact-form-container .form-card {
    padding: 28px;
  }
}

@media (max-width: 968px) {
  .main-content {
    padding-top: 180px; // 移动端头部较高
    
    .container {
      padding: 0 20px;
    }
  }
  
  .contact-form-container .form-card {
    padding: 24px;
    border-radius: 8px;
  }
  
  .contact-form .form-item {
    margin-bottom: 20px;
  }
  
  .action-area {
    margin-top: 24px;
    
    .send-btn {
      width: 100%;
      margin-bottom: 16px;
    }
    
    .additional-options {
      text-align: center;
    }
  }
}
</style> 