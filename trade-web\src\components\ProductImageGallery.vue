<template>
  <div class="product-image-gallery">
    <!-- 主图展示区域 -->
    <div class="main-image-container">
      <div class="main-image-wrapper">
        <img 
          :src="images[currentIndex]" 
          :alt="productName" 
          class="main-image"
          @load="handleImageLoad"
        />
        
        <!-- 左右切换按钮 -->
        <button 
          v-if="images.length > 1"
          class="nav-btn prev-btn" 
          @click="previousImage"
        >
          <el-icon><ArrowLeft /></el-icon>
        </button>
        
        <button 
          v-if="images.length > 1"
          class="nav-btn next-btn" 
          @click="nextImage"
        >
          <el-icon><ArrowRight /></el-icon>
        </button>
        
        <!-- 图片指示器 -->
        <div v-if="images.length > 1" class="image-indicators">
          <span 
            v-for="(image, index) in images" 
            :key="index"
            class="indicator"
            :class="{ active: index === currentIndex }"
            @click="goToImage(index)"
          ></span>
        </div>
      </div>
    </div>
    
    <!-- 缩略图列表 -->
    <div v-if="images.length > 1" class="thumbnail-container">
      <div class="thumbnail-wrapper">
        <div 
          v-for="(image, index) in images" 
          :key="index"
          class="thumbnail-item"
          :class="{ active: index === currentIndex }"
          @click="goToImage(index)"
        >
          <img :src="image" :alt="`${productName} ${index + 1}`" class="thumbnail-image" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

export default {
  name: 'ProductImageGallery',
  props: {
    images: {
      type: Array,
      required: true,
      default: () => []
    },
    productName: {
      type: String,
      default: 'Product'
    }
  },
  setup(props) {
    const currentIndex = ref(0)
    const imageLoaded = ref(false)
    
    const currentImage = computed(() => {
      return props.images[currentIndex.value] || ''
    })
    
    const goToImage = (index) => {
      if (index >= 0 && index < props.images.length) {
        currentIndex.value = index
        imageLoaded.value = false
      }
    }
    
    const nextImage = () => {
      if (currentIndex.value < props.images.length - 1) {
        goToImage(currentIndex.value + 1)
      } else {
        // 循环到第一张
        goToImage(0)
      }
    }
    
    const previousImage = () => {
      if (currentIndex.value > 0) {
        goToImage(currentIndex.value - 1)
      } else {
        // 循环到最后一张
        goToImage(props.images.length - 1)
      }
    }
    
    const handleImageLoad = () => {
      imageLoaded.value = true
    }
    
    return {
      currentIndex,
      currentImage,
      imageLoaded,
      goToImage,
      nextImage,
      previousImage,
      handleImageLoad
    }
  }
}
</script>

<style lang="scss" scoped>
.product-image-gallery {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: 500px; // 限制整体最大高度
}

.main-image-container {
  margin-bottom: 12px;
  flex: 1;
  max-height: calc(100% - 90px); // 为缩略图预留空间
  
  .main-image-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 75%; // 4:3比例
    max-height: 400px; // 限制最大高度
    border-radius: 12px;
    overflow: hidden;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    
    .main-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: opacity 0.3s ease;
    }
    
    .nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 2;
      
      &:hover {
        background: white;
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
      
      .el-icon {
        font-size: 18px;
        color: #374151;
      }
      
      &.prev-btn {
        left: 16px;
      }
      
      &.next-btn {
        right: 16px;
      }
    }
    
    .image-indicators {
      position: absolute;
      bottom: 16px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;
      z-index: 2;
      
      .indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.active {
          background: white;
          transform: scale(1.2);
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

.thumbnail-container {
  margin-top: auto; // 确保缩略图始终在底部
  min-height: 78px; // 确保有足够的空间显示缩略图
  
  .thumbnail-wrapper {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 4px 0;
    
    /* 隐藏滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    
    .thumbnail-item {
      flex: 0 0 70px;
      height: 70px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      
      &.active {
        border-color: #2563eb;
        transform: scale(1.05);
      }
      
      &:hover {
        transform: scale(1.02);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .thumbnail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// 响应式适配
@media (max-width: 1366px) {
  .product-image-gallery {
    max-height: 450px; // 调整整体最大高度
  }
  
  .main-image-container {
    max-height: calc(100% - 80px); // 为缩略图预留空间
    
    .main-image-wrapper {
      padding-bottom: 70%;
      max-height: 380px; // 限制最大高度
    }
  }
  
  .thumbnail-container {
    min-height: 73px; // 确保有足够的空间显示缩略图
    
    .thumbnail-wrapper .thumbnail-item {
      flex: 0 0 65px;
      height: 65px;
    }
  }
}

@media (max-width: 768px) {
  .product-image-gallery {
    max-height: 400px; // 调整整体最大高度
  }
  
  .main-image-container {
    margin-bottom: 10px;
    max-height: calc(100% - 70px); // 为缩略图预留空间
    
    .main-image-wrapper {
      padding-bottom: 65%;
      max-height: 320px; // 限制最大高度
      
      .nav-btn {
        width: 36px;
        height: 36px;
        
        .el-icon {
          font-size: 16px;
        }
        
        &.prev-btn {
          left: 12px;
        }
        
        &.next-btn {
          right: 12px;
        }
      }
    }
  }
  
  .thumbnail-container {
    min-height: 68px; // 确保有足够的空间显示缩略图
    
    .thumbnail-wrapper .thumbnail-item {
      flex: 0 0 60px;
      height: 60px;
    }
  }
}

@media (max-width: 480px) {
  .product-image-gallery {
    max-height: 350px; // 调整整体最大高度
  }
  
  .main-image-container {
    margin-bottom: 8px;
    max-height: calc(100% - 58px); // 为缩略图预留空间
    
    .main-image-wrapper {
      padding-bottom: 60%;
      max-height: 280px; // 限制最大高度
    }
  }
  
  .thumbnail-container {
    min-height: 56px; // 确保有足够的空间显示缩略图
    
    .thumbnail-wrapper {
      gap: 6px;
      
      .thumbnail-item {
        flex: 0 0 50px;
        height: 50px;
      }
    }
  }
}
</style> 