import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import CategoryDetailView from '../views/CategoryDetailView.vue'
import ProductDetailView from '../views/ProductDetailView.vue'
import ContactSupplierView from '../views/ContactSupplierView.vue'
import CompanyHomeView from '../views/CompanyHomeView.vue'
import MerchantTestView from '../views/MerchantTestView.vue'
import MerchantListView from '../views/MerchantListView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/merchants',
      name: 'merchants',
      component: MerchantListView,
    },
    {
      path: '/products',
      name: 'products',
      component: CategoryDetailView,
    },
    {
      path: '/category/:categoryId',
      name: 'category-detail',
      component: CategoryDetailView,
      props: true
    },
    {
      path: '/product/:productId',
      name: 'product-detail',
      component: ProductDetailView,
      props: true
    },
    {
      path: '/contact-supplier/:supplierId',
      name: 'contact-supplier',
      component: ContactSupplierView,
      props: true
    },
    {
      path: '/company/:companyId',
      name: 'company-home',
      component: CompanyHomeView,
      props: true
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/test-merchant',
      name: 'merchant-test',
      component: MerchantTestView,
    },
  ],
})

export default router
