<template>
  <div class="merchant-list">
    <HeaderNav :categories="categoryTree" />

    <main class="main-content">
      <div class="container">
        <div class="page-header">
          <div class="section-header">
            <h1 class="section-title">
              {{ currentLang === 'zh' ? '商家列表' : 'Merchants' }}
            </h1>
            <div class="product-count">
              {{ currentLang === 'zh' ? '共找到' : 'Found' }} {{ total }} {{ currentLang === 'zh' ? '家商户' : 'merchants' }}
            </div>
          </div>
        </div>

        <section class="results-section">
          <div v-if="loading" class="loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            {{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}
          </div>

          <div v-else-if="merchants.length === 0" class="empty-state">
            <el-icon><Box /></el-icon>
            <p>{{ currentLang === 'zh' ? '暂无商户' : 'No merchants found' }}</p>
          </div>

          <div v-else class="merchant-grid">
            <div v-for="m in merchants" :key="m.id" class="merchant-card">
              <div class="merchant-cover">
                <img :src="m.cover || defaultCover" :alt="m.name" />
                <img class="merchant-logo" :src="m.logo || defaultLogo" :alt="m.name" />
              </div>
              <div class="merchant-info">
                <h3 class="merchant-name">{{ m.name }}</h3>
                <p class="merchant-brief">{{ m.brief || (currentLang === 'zh' ? '暂无简介' : 'No brief') }}</p>
                <div class="merchant-meta">
                  <div class="meta-item">
                    <span class="label">{{ currentLang === 'zh' ? '联系人' : 'Contact' }}</span>
                    <span class="value">{{ m.contactName || '-' }}</span>
                  </div>
                  <div class="meta-item1">
                    <span class="value">
                      <img v-if="m.qrCode" :src="m.qrCode" class="qr-img" alt="qr" />
                      <span v-else>-</span>
                    </span>
                    <span class="label">QR</span>
                  </div>
                </div>
                <div class="merchant-actions">
                  <el-button type="primary" size="small" @click="goMerchant(m.id)">
                    {{ currentLang === 'zh' ? '主页' : 'Visit Store' }}
                  </el-button>
                  <el-button size="small" @click="contact(m.id)">
                    {{ currentLang === 'zh' ? '联系供应商' : 'Contact' }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="pagination-wrapper" v-if="total > pageSize">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :total="total"
              layout="prev, pager, next, jumper"
              @current-change="handlePageChange"
            />
          </div>
        </section>
      </div>
    </main>
  </div>
  
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, Box } from '@element-plus/icons-vue'
import HeaderNav from '../components/HeaderNav.vue'
import { getCategories } from '../api/home.js'
import { searchMerchants } from '../api/merchant.js'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'MerchantListView',
  components: { HeaderNav, Loading, Box },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const languageStore = useLanguageStore()

    const categoryTree = ref([])
    const merchants = ref([])
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)

    const defaultCover = 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?auto=format&fit=crop&w=1200&q=60'
    const defaultLogo = 'https://images.unsplash.com/photo-1549952462-77a0ae1cc1b3?auto=format&fit=crop&w=256&q=60'

    const currentLang = computed(() => languageStore.currentLang)

    const loadCategories = async () => {
      try {
        const res = await getCategories()
        if (res && res.code === 0) categoryTree.value = res.data || []
      } catch (e) {
        console.error('加载分类失败:', e)
      }
    }

    const getAttrValueById = (attributes, attributeId) => {
      const attr = (attributes || []).find(a => a.attribute_id === attributeId || a.attribute?.id === attributeId)
      return attr ? attr.value : null
    }

    const normalizeMerchant = (raw) => {
      const name = getAttrValueById(raw.attributes, 1) || raw.name || 'Merchant'
      const logo = getAttrValueById(raw.attributes, 2)
      const cover = getAttrValueById(raw.attributes, 3)
      const brief = getAttrValueById(raw.attributes, 4)
      const qrCode = getAttrValueById(raw.attributes, 21)
      const contactName = getAttrValueById(raw.attributes, 20)
      return {
        id: raw.id,
        name,
        logo,
        cover,
        brief,
        qrCode,
        contactName
      }
    }

    const search = async () => {
      try {
        loading.value = true
        const name = (route.query.search || '').toString()
        const res = await searchMerchants({ page: currentPage.value, size: pageSize.value, name })
        if (res && res.code === 0) {
          const list = res.data?.data || []
          merchants.value = list.map(normalizeMerchant)
          total.value = res.data?.total || 0
        } else {
          merchants.value = []
          total.value = 0
        }
      } catch (e) {
        console.error('搜索商户失败:', e)
        merchants.value = []
        total.value = 0
      } finally {
        loading.value = false
      }
    }

    const handlePageChange = (page) => {
      currentPage.value = page
      search()
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }

    onMounted(() => {
      languageStore.initLanguage()
      loadCategories()
      // 支持直接通过 search 查询参数进入商家搜索
      currentPage.value = parseInt(route.query.page || '1') || 1
      search()
    })

    watch(() => route.query, () => {
      currentPage.value = 1
      search()
    })

    const goMerchant = (id) => router.push(`/company/${id}`)
    const contact = (id) => router.push(`/contact-supplier/${id}`)

    return {
      categoryTree,
      merchants,
      loading,
      currentPage,
      pageSize,
      total,
      currentLang,
      defaultCover,
      defaultLogo,
      handlePageChange,
      goMerchant,
      contact
    }
  }
}
</script>

<style lang="scss" scoped>
.merchant-list {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.main-content {
  padding-top: 140px;
  padding-bottom: 48px;

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
  }
  .page-header { margin-bottom: 24px; padding-bottom: 16px; border-bottom: 1px solid #e5e7eb; }
}

.merchant-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.merchant-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
  transition: transform .2s ease, box-shadow .2s ease;

  &:hover { transform: translateY(-4px); box-shadow: 0 10px 25px rgba(0,0,0,.12); }

  .merchant-cover {
    position: relative;
    height: 160px;
    background: #f8fafc;
    img { width: 100%; height: 100%; object-fit: cover; display: block; }
    .merchant-logo {
      position: absolute;
      left: 16px; bottom: -20px;
      width: 56px; height: 56px; border-radius: 8px; border: 2px solid #fff; background: #fff; object-fit: cover;
    }
  }

  .merchant-info {
    padding: 28px 16px 16px 16px;
    .merchant-name { margin: 0 0 6px 0; font-size: 18px; font-weight: 700; color: #1f2937; }
    .merchant-brief { margin: 0 0 10px 0; font-size: 14px; color: #6b7280; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }
    .merchant-meta { display: grid; grid-template-columns: 1fr auto; gap: 10px; align-items: center; }
    .meta-item { display: flex; align-items: center; gap: 6px; .label { color: #64748b; font-size: 13px; } .value { font-size: 14px; color: #334155; } }
    .meta-item1 { display: flex; flex-direction: column; align-items: center; gap: 6px; .label { color: #64748b; font-size: 13px; } .value { font-size: 14px; color: #334155; } }
    .qr-img { width: 50px; height: 50px; object-fit: cover; border: 1px solid #e5e7eb; border-radius: 6px; }
    .merchant-actions { margin-top: 12px; display: flex; gap: 10px; }
  }
}

@media (max-width: 1024px) { .merchant-grid { grid-template-columns: repeat(2, 1fr); } }
@media (max-width: 640px) { .merchant-grid { grid-template-columns: 1fr; } }
</style>


