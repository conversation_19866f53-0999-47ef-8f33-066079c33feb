{"swagger": "2.0", "info": {"description": "trade-admin", "title": "trade-admin API", "contact": {}, "version": "0.0.1"}, "host": "localhost:8090", "basePath": "/", "paths": {"/v1/internal/attribute": {"get": {"description": "查询商品属性列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Attribute"], "summary": "商品属性列表", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.AttributeSetListResp"}}}}]}}}}}, "/v1/internal/attribute_set": {"get": {"description": "商家查询属性集合列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AttributeSet"], "summary": "商家属性集合列表", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.AttributeSetListResp"}}}}]}}}}}, "/v1/internal/attribute_set/type": {"get": {"description": "查询属性集合列表，支持多语言和type过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AttributeSet"], "summary": "商家分类属性集合列表", "parameters": [{"type": "string", "description": "属性类型", "name": "type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.AttributeSetListResp"}}}}]}}}}}, "/v1/internal/banner": {"get": {"description": "根据条件查询Banner列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Banner"], "summary": "查询Banner列表", "parameters": [{"type": "string", "description": "名称", "name": "name", "in": "query"}, {"type": "integer", "description": "页码，默认1", "name": "page", "in": "query"}, {"type": "integer", "description": "每页大小，默认10", "name": "size", "in": "query"}], "responses": {"200": {"description": "获取Banner列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/db.Banner"}}}}]}}}}, "post": {"description": "创建新的Banner", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Banner"], "summary": "创建Banner", "parameters": [{"description": "Banner信息", "name": "banner", "in": "body", "required": true, "schema": {"$ref": "#/definitions/db.Banner"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Banner"}}}]}}}}}, "/v1/internal/banner/{id}": {"get": {"description": "根据ID获取Banner详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Banner"], "summary": "获取Banner详情", "parameters": [{"type": "integer", "description": "Banner ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取Banner详情", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Banner"}}}]}}}}, "put": {"description": "根据ID更新Banner信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Banner"], "summary": "更新Banner", "parameters": [{"type": "integer", "description": "Banner ID", "name": "id", "in": "path", "required": true}, {"description": "Banner信息", "name": "banner", "in": "body", "required": true, "schema": {"$ref": "#/definitions/db.Banner"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Banner"}}}]}}}}, "delete": {"description": "根据ID删除Banner", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Banner"], "summary": "删除Banner", "parameters": [{"type": "integer", "description": "Banner ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/v1/internal/category": {"get": {"description": "查询获取目录列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Category"], "summary": "查询获取目录列表", "parameters": [{"type": "string", "description": "目录名称", "name": "name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}], "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/db.Category"}}}}]}}}}, "post": {"description": "新增目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Category"], "summary": "新增目录", "parameters": [{"description": "目录信息", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CategoryCreateReq"}}], "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Category"}}}]}}}}}, "/v1/internal/category/tree": {"get": {"description": "获取目录树结构，包含子目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Category"], "summary": "获取目录树结构", "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.Category"}}}}]}}}}}, "/v1/internal/category/{id}": {"get": {"description": "获取目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Category"], "summary": "获取目录", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取目录", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.Category"}}}]}}}}, "put": {"description": "更新目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Category"], "summary": "更新目录", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"description": "更新信息", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CategoryUpdateReq"}}], "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Category"}}}]}}}}, "delete": {"description": "删除目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Category"], "summary": "删除目录", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/v1/internal/image/delete": {"delete": {"description": "根据图片 URL 删除 MinIO 中的图片", "produces": ["application/json"], "tags": ["Image"], "summary": "删除图片", "parameters": [{"type": "string", "description": "图片 URL", "name": "image", "in": "query", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/image/upload": {"post": {"description": "上传图片到 MinIO，并返回图片 URL", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Image"], "summary": "上传图片", "parameters": [{"type": "file", "description": "图片文件", "name": "image", "in": "formData", "required": true}], "responses": {"200": {"description": "上传成功，返回图片 URL", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/merchant": {"get": {"description": "查询获取商户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "获取商户列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}, {"type": "string", "description": "商户名称", "name": "name", "in": "query"}, {"type": "integer", "description": "商户状态", "name": "status", "in": "query"}], "responses": {"200": {"description": "商户列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/db.Merchant"}}}}]}}}}, "post": {"description": "新增商户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "新增商户", "parameters": [{"description": "商户信息", "name": "merchant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.MerchantCreateReq"}}], "responses": {"200": {"description": "创建结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Merchant"}}}]}}}}}, "/v1/internal/merchant/category": {"post": {"description": "新增商家目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantCategory"], "summary": "新增商家目录", "parameters": [{"description": "商家目录信息", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/db.MerchantCategory"}}], "responses": {"200": {"description": "创建成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.MerchantCategory"}}}]}}}}}, "/v1/internal/merchant/category/tree": {"get": {"description": "根据 merchant_id, language 获取商家目录树结构", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantCategory"], "summary": "获取商家目录树结构", "parameters": [{"type": "integer", "description": "商家ID", "name": "merchant_id", "in": "query", "required": true}, {"type": "string", "description": "语言", "name": "language", "in": "query", "required": true}], "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.MerchantCategoryNode"}}}}]}}}}}, "/v1/internal/merchant/category/{id}": {"get": {"description": "获取商家目录详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantCategory"], "summary": "获取商家目录详情", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.MerchantCategory"}}}]}}}}, "put": {"description": "更新商家目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantCategory"], "summary": "更新商家目录", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}, {"description": "更新内容", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/db.MerchantCategory"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.MerchantCategory"}}}]}}}}, "delete": {"description": "删除商家目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantCategory"], "summary": "删除商家目录", "parameters": [{"type": "integer", "description": "id", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/v1/internal/merchant/{id}": {"get": {"description": "获取单个商户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "获取商户", "parameters": [{"type": "integer", "description": "商户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "商户信息", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Merchant"}}}]}}}}, "put": {"description": "更新商户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "更新商户", "parameters": [{"type": "integer", "description": "商户ID", "name": "id", "in": "path", "required": true}, {"description": "商户信息", "name": "merchant", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.MerchantUpdateReq"}}], "responses": {"200": {"description": "更新结果", "schema": {"$ref": "#/definitions/result.Result"}}}}, "delete": {"description": "删除商户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "删除商户", "parameters": [{"type": "integer", "description": "商户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除结果", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/merchant/{id}/attribute": {"get": {"description": "查询获取商家属性列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantAttribute"], "summary": "获取商家属性列表", "parameters": [{"type": "integer", "description": "商家ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "语言代码", "name": "language", "in": "query"}], "responses": {"200": {"description": "商家属性列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.MerchantAttribute"}}}}]}}}}, "put": {"description": "更新商家属性值", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantAttribute"], "summary": "更新商家属性", "parameters": [{"type": "integer", "description": "商家ID", "name": "id", "in": "path", "required": true}, {"description": "商家属性更新信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.MerchantAttributeReq"}}], "responses": {"200": {"description": "更新结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}, "post": {"description": "为商家创建属性值（支持语言）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["MerchantAttribute"], "summary": "创建商家属性（支持语言）", "parameters": [{"type": "integer", "description": "商家ID", "name": "id", "in": "path", "required": true}, {"description": "商家属性信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.MerchantAttributeReq"}}], "responses": {"200": {"description": "创建结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/v1/internal/product": {"get": {"description": "查询获取商品列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "获取商品列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}], "responses": {"200": {"description": "商品列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.ProductInfo"}}}}]}}}}, "post": {"description": "新增商品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "新增商品", "parameters": [{"description": "商品信息", "name": "product", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductCreateReq"}}], "responses": {"200": {"description": "创建结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.Product"}}}]}}}}}, "/v1/internal/product/price/{id}": {"get": {"description": "根据价格ID获取单条商品价格记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductPrice"], "summary": "获取商品价格详情", "parameters": [{"type": "integer", "description": "价格ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "商品价格详情", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.PriceInfo"}}}]}}}}, "put": {"description": "更新商品价格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductPrice"], "summary": "更新商品价格", "parameters": [{"type": "integer", "description": "价格ID", "name": "id", "in": "path", "required": true}, {"description": "商品价格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductPriceReq"}}], "responses": {"200": {"description": "更新成功", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/product/{id}": {"get": {"description": "获取单个商品信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "获取商品", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "商品信息", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.ProductInfo"}}}]}}}}, "put": {"description": "更新商品信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "更新商品", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"description": "商品信息", "name": "product", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductUpdateReq"}}], "responses": {"200": {"description": "更新结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/db.Product"}}}]}}}}, "delete": {"description": "删除商品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "删除商品", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/v1/internal/product/{id}/attribute": {"get": {"description": "查询获取商品属性列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductAttribute"], "summary": "获取商品属性列表", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "语言代码", "name": "language", "in": "query"}], "responses": {"200": {"description": "商品属性列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.ProductAttribute"}}}}]}}}}, "put": {"description": "更新商品属性值（非多语言）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductAttribute"], "summary": "更新商品属性（非多语言）", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"description": "商品属性更新信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductAttributeReq"}}], "responses": {"200": {"description": "更新结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}, "post": {"description": "为商品创建属性值（支持语言）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductAttribute"], "summary": "创建商品属性（支持语言）", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"description": "商品属性信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductAttributeReq"}}], "responses": {"200": {"description": "创建结果", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "boolean"}}}]}}}}}, "/v1/internal/product/{id}/price": {"post": {"description": "为商品创建价格信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductPrice"], "summary": "创建商品价格", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"description": "商品价格信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductPriceReq"}}], "responses": {"200": {"description": "创建成功", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/product/{id}/prices": {"get": {"description": "根据商品ID获取价格列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ProductPrice"], "summary": "获取商品价格列表", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "商品价格列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.PriceInfo"}}}}]}}}}}, "/v1/internal/product/{id}/status": {"patch": {"description": "更新商品状态", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "更新商品状态", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"description": "商品状态信息", "name": "product", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ProductUpdateStatusReq"}}], "responses": {"200": {"description": "更新结果", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/template": {"get": {"description": "获取模板列表，支持分页和名称搜索", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "获取模板列表", "parameters": [{"type": "string", "description": "模板名称", "name": "name", "in": "query"}, {"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}], "responses": {"200": {"description": "获取模板列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.TemplateRes"}}}}]}}}}, "post": {"description": "创建新的商品模板", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "创建模板", "parameters": [{"description": "模板信息", "name": "template", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.TemplateCreateReq"}}], "responses": {"200": {"description": "创建模板", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "integer"}}}]}}}}}, "/v1/internal/template/attribute": {"post": {"description": "为模板添加单个属性", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "添加模板属性", "parameters": [{"description": "模板属性信息", "name": "templateAttribute", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.TemplateAttributeCreateReq"}}], "responses": {"200": {"description": "添加模板属性", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "integer"}}}]}}}}}, "/v1/internal/template/attribute/{id}": {"put": {"description": "更新模板属性的排序等信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "更新模板属性", "parameters": [{"type": "integer", "description": "模板属性ID", "name": "id", "in": "path", "required": true}, {"description": "模板属性信息", "name": "templateAttribute", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.TemplateAttributeUpdateReq"}}], "responses": {"200": {"description": "更新模板属性", "schema": {"$ref": "#/definitions/result.Result"}}}}, "delete": {"description": "删除模板属性关联", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "删除模板属性", "parameters": [{"type": "integer", "description": "模板属性ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除模板属性", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/template/batch-attributes": {"post": {"description": "为模板批量添加属性", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "批量添加模板属性", "parameters": [{"description": "模板属性列表", "name": "templateAttributes", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.TemplateBatchAttributesReq"}}], "responses": {"200": {"description": "批量添加模板属性", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/template/{id}": {"get": {"description": "获取模板详情，包含关联的属性", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "获取模板详情", "parameters": [{"type": "integer", "description": "模板ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "获取模板详情", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.TemplateRes"}}}]}}}}, "put": {"description": "更新模板信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "更新模板", "parameters": [{"type": "integer", "description": "模板ID", "name": "id", "in": "path", "required": true}, {"description": "模板信息", "name": "template", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.TemplateUpdateReq"}}], "responses": {"200": {"description": "更新模板", "schema": {"$ref": "#/definitions/result.Result"}}}}, "delete": {"description": "删除模板及其关联的属性", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Template"], "summary": "删除模板", "parameters": [{"type": "integer", "description": "模板ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "删除模板", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/user/info": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "获取用户信息", "responses": {"200": {"description": "获取成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.UserInfo"}}}]}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/result.Result"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/result.Result"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/result.Result"}}}}, "put": {"security": [{"Bearer": []}], "description": "更新当前登录用户的信息（用户名、电话）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "更新用户信息", "parameters": [{"description": "用户更新信息", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UserUpdateRequest"}}], "responses": {"200": {"description": "更新成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.UserInfo"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/result.Result"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/result.Result"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/result.Result"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/user/logout": {"post": {"security": [{"Bearer": []}], "description": "用户登出，将token加入黑名单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "用户登出", "responses": {"200": {"description": "登出成功", "schema": {"$ref": "#/definitions/result.Result"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/internal/user/password": {"put": {"security": [{"Bearer": []}], "description": "修改当前登录用户的密码，新密码要求：长度不少于8位，必须包含至少2个字母和2个数字，不能是简单密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "修改密码", "parameters": [{"description": "密码修改信息", "name": "password", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ChangePasswordRequest"}}], "responses": {"200": {"description": "修改成功", "schema": {"$ref": "#/definitions/result.Result"}}, "400": {"description": "参数错误或原密码错误", "schema": {"$ref": "#/definitions/result.Result"}}, "401": {"description": "未授权", "schema": {"$ref": "#/definitions/result.Result"}}, "404": {"description": "用户不存在", "schema": {"$ref": "#/definitions/result.Result"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/public/currency": {"get": {"description": "获取所有可用的货币列表，供前端使用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "获取所有货币列表", "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/apiweb.Currency"}}}}]}}}}}, "/v1/public/home/<USER>": {"get": {"description": "获取目录树结构，包含子目录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Homepage"], "summary": "获取目录树结构", "parameters": [{"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}], "responses": {"200": {"description": "获取列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.Category"}}}}]}}}}}, "/v1/public/home/<USER>/category": {"get": {"description": "获取热门目录列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Homepage"], "summary": "获取热门目录列表", "parameters": [{"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}], "responses": {"200": {"description": "获取热门目录列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.Category"}}}}]}}}}}, "/v1/public/home/<USER>/showcase": {"get": {"description": "获取企业风采展示", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Homepage"], "summary": "企业风采展示", "parameters": [{"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}], "responses": {"200": {"description": "获取企业风采展示", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.MerchantDetail"}}}}]}}}}}, "/v1/public/merchant/product/search": {"get": {"description": "查询商户商品列表，支持多语言和分页。商品名称通过商品属性表中属性ID=1的值进行模糊匹配", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "[web]查询商户商品列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}, {"type": "string", "description": "商品名称（通过商品属性ID=1的值进行模糊匹配）", "name": "name", "in": "query"}, {"type": "integer", "description": "商户ID", "name": "merchant_id", "in": "query", "required": true}, {"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}], "responses": {"200": {"description": "商户商品列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/result.Page"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.ProductDetail"}}}}]}}}]}}}}}, "/v1/public/merchant/search": {"get": {"description": "查询获取商户列表，支持多语言和分页。商户名称通过商户属性表中属性ID=1的值进行模糊匹配", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "[web]查询商户列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}, {"type": "string", "description": "商户名称（通过商户属性ID=1的值进行模糊匹配）", "name": "name", "in": "query"}, {"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}], "responses": {"200": {"description": "商户列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/result.Page"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.MerchantDetail"}}}}]}}}]}}}}}, "/v1/public/merchant/{id}": {"get": {"description": "根据商户ID获取商户详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Merchant"], "summary": "[web]获取商户详情", "parameters": [{"type": "integer", "description": "商户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "商户详情", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.MerchantDetail"}}}]}}}}}, "/v1/public/product/search": {"get": {"description": "查询获取商品列表，支持多语言和属性信息。商品名称通过商品属性表中属性ID=1的值进行模糊匹配", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "[web]查询商品列表", "parameters": [{"type": "integer", "description": "页码", "name": "page", "in": "query"}, {"type": "integer", "description": "每页数量", "name": "size", "in": "query"}, {"type": "string", "description": "商品名称（通过商品属性ID=1的值进行模糊匹配）", "name": "name", "in": "query"}, {"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}, {"type": "integer", "description": "一级分类ID", "name": "category", "in": "query"}, {"type": "integer", "description": "二级分类ID", "name": "sub_category", "in": "query"}, {"type": "integer", "description": "三级分类ID", "name": "thd_category", "in": "query"}], "responses": {"200": {"description": "商品列表", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/result.Page"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/dto.ProductCard"}}}}]}}}]}}}}}, "/v1/public/product/{id}": {"get": {"description": "根据商品ID获取商品详情", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Product"], "summary": "[web]获取商品详情", "parameters": [{"type": "integer", "description": "商品ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "语言代码", "name": "Accept-Language", "in": "header", "required": true}], "responses": {"200": {"description": "商品详情", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.ProductDetail"}}}]}}}}}, "/v1/public/user/login": {"post": {"description": "用户通过邮箱和密码登录，返回JWT token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "用户登录", "parameters": [{"description": "用户登录信息", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UserLoginRequest"}}], "responses": {"200": {"description": "登录成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.UserLoginResponse"}}}]}}, "400": {"description": "邮箱或密码错误", "schema": {"$ref": "#/definitions/result.Result"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/public/user/refresh": {"post": {"description": "使用refresh token获取新的access token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "刷新token", "parameters": [{"description": "刷新token请求", "name": "refresh", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.RefreshTokenRequest"}}], "responses": {"200": {"description": "刷新成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.RefreshTokenResponse"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/result.Result"}}, "401": {"description": "无效的refresh token", "schema": {"$ref": "#/definitions/result.Result"}}}}}, "/v1/public/user/register": {"post": {"description": "用户注册接口，支持用户名、邮箱、电话注册。密码要求：长度不少于8位，必须包含至少2个字母和2个数字，不能是简单密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "用户注册", "parameters": [{"description": "用户注册信息", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.UserRegisterRequest"}}], "responses": {"200": {"description": "注册成功", "schema": {"allOf": [{"$ref": "#/definitions/result.Result"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/dto.UserInfo"}}}]}}, "400": {"description": "参数错误", "schema": {"$ref": "#/definitions/result.Result"}}, "500": {"description": "内部服务器错误", "schema": {"$ref": "#/definitions/result.Result"}}}}}}, "definitions": {"apiweb.Currency": {"type": "object", "properties": {"code": {"type": "string"}, "country": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "sort_num": {"type": "integer"}, "status": {"type": "integer"}, "symbol": {"type": "string"}}}, "db.Banner": {"type": "object", "properties": {"create_time": {"type": "string"}, "deeplink": {"type": "string"}, "id": {"type": "integer"}, "image": {"type": "string"}, "name": {"type": "string"}, "sort_num": {"type": "integer"}, "status": {"type": "integer"}, "update_time": {"type": "string"}}}, "db.Category": {"type": "object", "properties": {"category_url": {"type": "string"}, "create_time": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "image": {"type": "string"}, "level": {"type": "integer"}, "logo": {"type": "string"}, "name": {"type": "string"}, "parent_category_id": {"type": "integer"}, "sort_num": {"type": "integer"}, "status": {"type": "integer"}, "update_time": {"type": "string"}}}, "db.Merchant": {"type": "object", "properties": {"create_time": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "status": {"type": "integer"}, "update_time": {"type": "string"}}}, "db.MerchantCategory": {"type": "object", "properties": {"create_time": {"type": "string"}, "id": {"type": "integer"}, "level": {"type": "integer"}, "logo": {"type": "string"}, "merchant_id": {"type": "integer"}, "name": {"type": "string"}, "parent_category_id": {"type": "integer"}, "status": {"type": "integer"}, "update_time": {"type": "string"}}}, "db.Product": {"type": "object", "properties": {"create_time": {"type": "string"}, "id": {"type": "integer"}, "name": {"description": "新增名字字段", "type": "string"}, "status": {"type": "integer"}, "template_id": {"description": "商品模板ID", "type": "integer"}, "update_time": {"type": "string"}}}, "dto.Attribute": {"type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer"}, "is_i18n": {"type": "integer"}, "is_image": {"type": "integer"}, "is_multi_text": {"type": "integer"}, "is_video": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}, "value_type": {"type": "string"}}}, "dto.AttributeRes": {"type": "object", "properties": {"description": {"description": "属性描述", "type": "string"}, "id": {"description": "属性ID", "type": "integer"}, "is_i18n": {"description": "是否多语言 0-否 1-是", "type": "integer"}, "is_image": {"description": "是否图片属性 0-否 1-是", "type": "integer"}, "is_multi_text": {"description": "是否富文本 0-否 1-是", "type": "integer"}, "is_video": {"description": "是否视频属性 0-否 1-是", "type": "integer"}, "name": {"description": "属性名称", "type": "string"}, "status": {"description": "属性状态 0-不可用 1-可用", "type": "integer"}, "type": {"description": "属性类别 base-基础属性 sale-销售属性 specification-规格属性", "type": "string"}, "value_type": {"description": "属性值类型 string/int/float/boolean/date/string[]", "type": "string"}}}, "dto.AttributeSet": {"type": "object", "properties": {"description": {"type": "string"}, "id": {"type": "integer"}, "is_i18n": {"type": "integer"}, "is_image": {"type": "integer"}, "is_multi_text": {"type": "integer"}, "is_video": {"type": "integer"}, "name": {"type": "string"}, "status": {"type": "integer"}, "type": {"type": "string"}, "value_type": {"type": "string"}}}, "dto.AttributeSetListResp": {"type": "object", "properties": {"create_time": {"description": "创建时间", "type": "string"}, "description": {"description": "属性描述", "type": "string"}, "id": {"description": "属性ID", "type": "integer"}, "is_i18n": {"description": "是否多语言 0-否 1-是", "type": "integer"}, "is_image": {"description": "是否图片属性 0-否 1-是，上传图片后上传链接", "type": "integer"}, "is_multi_text": {"description": "是否富文本 0-否 1-是，富文本编辑内容上传", "type": "integer"}, "is_video": {"description": "是否视频属性 0-否 1-是，上传视频后上传链接", "type": "integer"}, "name": {"description": "属性名称", "type": "string"}, "status": {"description": "属性状态 0-不可用 1-可用", "type": "integer"}, "type": {"description": "属性类别 base-基础属性 contact-联系信息 company-公司属性", "type": "string"}, "update_time": {"description": "修改时间", "type": "string"}, "value_type": {"description": "属性值类型 string/int/float/bool/date/string[]/int[]/float[]", "type": "string"}}}, "dto.Banner": {"type": "object", "properties": {"deeplink": {"description": "跳转链接", "type": "string"}, "id": {"description": "横幅ID", "type": "integer"}, "image": {"description": "横幅图片链接", "type": "string"}, "name": {"description": "横幅名称", "type": "string"}, "sort_num": {"description": "排序号", "type": "integer"}, "status": {"description": "横幅状态 0-禁用 1-启用", "type": "integer"}}}, "dto.Category": {"type": "object", "properties": {"category_url": {"description": "分类URL", "type": "string"}, "description": {"description": "分类描述", "type": "string"}, "id": {"description": "分类ID", "type": "integer"}, "image": {"description": "分类图片", "type": "string"}, "level": {"description": "分类层级 1-一级 2-二级 3-三级", "type": "integer"}, "logo": {"description": "分类Logo", "type": "string"}, "name": {"description": "分类名称", "type": "string"}, "parent_category_id": {"description": "父分类ID", "type": "integer"}, "sort_num": {"description": "排序号", "type": "integer"}, "status": {"description": "分类状态 0-禁用 1-启用", "type": "integer"}, "sub_categories": {"description": "子分类列表", "type": "array", "items": {"$ref": "#/definitions/dto.Category"}}, "template_id": {"description": "关联的模板ID", "type": "integer"}}}, "dto.CategoryCreateReq": {"type": "object", "required": ["name"], "properties": {"category_url": {"description": "分类URL", "type": "string"}, "description": {"description": "分类描述", "type": "string"}, "image": {"description": "分类图片", "type": "string"}, "level": {"description": "分类层级 1-一级 2-二级 3-三级", "type": "integer"}, "logo": {"description": "分类Logo", "type": "string"}, "name": {"description": "分类名称\nRequired: true", "type": "string"}, "parent_category_id": {"description": "父分类ID，一级分类传0", "type": "integer"}, "sort_num": {"description": "排序号", "type": "integer"}, "status": {"description": "分类状态 0-禁用 1-启用", "type": "integer"}, "template_id": {"description": "关联的模板ID", "type": "integer"}}}, "dto.CategoryUpdateReq": {"type": "object", "required": ["id", "name"], "properties": {"category_url": {"description": "分类URL", "type": "string"}, "description": {"description": "分类描述", "type": "string"}, "id": {"description": "分类ID\nRequired: true", "type": "integer"}, "image": {"description": "分类图片", "type": "string"}, "level": {"description": "分类层级 1-一级 2-二级 3-三级", "type": "integer"}, "logo": {"description": "分类Logo", "type": "string"}, "name": {"description": "分类名称\nRequired: true", "type": "string"}, "parent_category_id": {"description": "父分类ID，一级分类传0", "type": "integer"}, "sort_num": {"description": "排序号", "type": "integer"}, "status": {"description": "分类状态 0-禁用 1-启用", "type": "integer"}, "template_id": {"description": "关联的模板ID", "type": "integer"}}}, "dto.ChangePasswordRequest": {"type": "object", "required": ["new_password", "old_password"], "properties": {"new_password": {"type": "string", "minLength": 8}, "old_password": {"type": "string"}}}, "dto.MerchantAttribute": {"type": "object", "properties": {"attribute": {"description": "属性详细信息", "allOf": [{"$ref": "#/definitions/dto.AttributeSet"}]}, "attribute_id": {"description": "属性ID", "type": "integer"}, "id": {"description": "商家属性ID", "type": "integer"}, "language": {"description": "语言代码", "type": "string"}, "merchant_id": {"description": "商家ID", "type": "integer"}, "value": {"description": "属性值", "type": "string"}}}, "dto.MerchantAttributeReq": {"type": "object", "required": ["attributes", "language"], "properties": {"attributes": {"description": "属性值列表\nRequired: true", "type": "array", "items": {"$ref": "#/definitions/dto.MerchantAttributeValue"}}, "is_default": {"description": "是否为默认语言 0-否 1-是", "type": "integer"}, "language": {"description": "语言代码\nRequired: true", "type": "string"}, "merchant_id": {"description": "商家ID", "type": "integer"}}}, "dto.MerchantAttributeValue": {"type": "object", "properties": {"attribute_id": {"description": "属性ID", "type": "integer"}, "value": {"description": "属性值", "type": "string"}}}, "dto.MerchantCategoryNode": {"type": "object", "properties": {"create_time": {"description": "创建时间", "type": "string"}, "id": {"description": "商家分类ID", "type": "integer"}, "level": {"description": "分类层级 1-一级 2-二级 3-三级", "type": "integer"}, "logo": {"description": "分类Logo", "type": "string"}, "merchant_id": {"description": "商家ID", "type": "integer"}, "name": {"description": "分类名称", "type": "string"}, "parent_category_id": {"description": "父分类ID", "type": "integer"}, "status": {"description": "分类状态 0-禁用 1-启用", "type": "integer"}, "sub_categories": {"description": "子分类列表", "type": "array", "items": {"$ref": "#/definitions/dto.MerchantCategoryNode"}}, "update_time": {"description": "更新时间", "type": "string"}}}, "dto.MerchantCreateReq": {"type": "object", "required": ["name"], "properties": {"name": {"description": "商家名称\nRequired: true", "type": "string"}}}, "dto.MerchantDetail": {"type": "object", "properties": {"attributes": {"description": "商家属性列表", "type": "array", "items": {"$ref": "#/definitions/dto.MerchantAttribute"}}, "id": {"description": "商家ID", "type": "integer"}, "name": {"description": "商家名称", "type": "string"}}}, "dto.MerchantUpdateReq": {"type": "object", "required": ["id", "name"], "properties": {"id": {"description": "商家ID\nRequired: true", "type": "integer"}, "name": {"description": "商家名称\nRequired: true", "type": "string"}, "status": {"description": "商家状态 0-禁用 1-启用", "type": "integer"}}}, "dto.PriceInfo": {"type": "object", "properties": {"currency": {"description": "货币代码 如USD、CNY等", "type": "string"}, "id": {"description": "价格ID", "type": "integer"}, "order_max": {"description": "最大订购量", "type": "integer"}, "order_min": {"description": "最小起订量", "type": "integer"}, "price_max": {"description": "最高价格（分为单位）", "type": "integer"}, "price_min": {"description": "最低价格（分为单位）", "type": "integer"}, "price_unit": {"description": "价格单位 如piece、kg、ton等", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}}}, "dto.Product": {"type": "object", "properties": {"category_id": {"description": "一级分类ID", "type": "integer"}, "id": {"description": "商品ID", "type": "integer"}, "merchant_category_id": {"description": "商家商品分类ID", "type": "integer"}, "merchant_id": {"description": "商家ID", "type": "integer"}, "name": {"description": "商品名称", "type": "string"}, "sec_category_id": {"description": "二级分类ID", "type": "integer"}, "sort_num": {"description": "在商家分类下的排序号", "type": "integer"}, "status": {"description": "商品状态 0-待上架 1-上架 2-下架", "type": "integer"}, "template_id": {"description": "商品模板ID", "type": "integer"}, "thd_category_id": {"description": "三级分类ID", "type": "integer"}}}, "dto.ProductAttribute": {"type": "object", "properties": {"attribute": {"description": "属性详细信息", "allOf": [{"$ref": "#/definitions/dto.Attribute"}]}, "attribute_id": {"description": "属性ID", "type": "integer"}, "id": {"description": "商品属性ID", "type": "integer"}, "language": {"description": "语言代码", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}, "value": {"description": "属性值", "type": "string"}}}, "dto.ProductAttributeReq": {"type": "object", "required": ["attributes", "language", "product_id"], "properties": {"attributes": {"description": "属性值列表\nRequired: true", "type": "array", "items": {"$ref": "#/definitions/dto.ProductAttributeValue"}}, "is_default": {"description": "是否为默认语言 0-否 1-是", "type": "integer"}, "language": {"description": "语言代码\nRequired: true", "type": "string"}, "product_id": {"description": "商品ID\nRequired: true", "type": "integer"}}}, "dto.ProductAttributeValue": {"type": "object", "properties": {"attribute_id": {"description": "属性ID", "type": "integer"}, "value": {"description": "属性值", "type": "string"}}}, "dto.ProductCard": {"type": "object", "properties": {"attributes": {"description": "商品属性列表", "type": "array", "items": {"$ref": "#/definitions/dto.ProductAttribute"}}, "merchant": {"description": "商户信息", "allOf": [{"$ref": "#/definitions/dto.MerchantDetail"}]}, "prices": {"description": "商品价格列表", "type": "array", "items": {"$ref": "#/definitions/dto.PriceInfo"}}, "product": {"description": "商品信息", "allOf": [{"$ref": "#/definitions/dto.Product"}]}}}, "dto.ProductCreateReq": {"type": "object", "properties": {"category_id": {"description": "商品目录\nRequired: true", "type": "integer"}, "merchant_category_id": {"description": "商家商品二级目录\nRequired: true", "type": "integer"}, "merchant_id": {"description": "商家ID\nRequired: true", "type": "integer"}, "name": {"description": "商品名称\nRequired: true", "type": "string"}, "sec_category_id": {"description": "商品二级目录\nRequired: true", "type": "integer"}, "sort_num": {"description": "商家商品二级目录下排序\nRequired: true", "type": "integer"}, "status": {"type": "integer"}, "template_id": {"description": "商品模板ID\nRequired: true", "type": "integer"}, "thd_category_id": {"description": "商品三级目录\nRequired: true", "type": "integer"}}}, "dto.ProductDetail": {"type": "object", "properties": {"attributes": {"description": "商品属性列表", "type": "array", "items": {"$ref": "#/definitions/dto.ProductAttribute"}}, "prices": {"description": "商品价格列表", "type": "array", "items": {"$ref": "#/definitions/dto.PriceInfo"}}, "product": {"description": "商品信息", "allOf": [{"$ref": "#/definitions/dto.Product"}]}}}, "dto.ProductInfo": {"type": "object", "properties": {"category_id": {"description": "一级分类ID", "type": "integer"}, "category_name": {"description": "一级分类名称", "type": "string"}, "id": {"description": "商品ID", "type": "integer"}, "merchant_category_id": {"description": "商家商品分类ID", "type": "integer"}, "merchant_category_name": {"description": "商家商品分类名称", "type": "string"}, "merchant_id": {"description": "商家ID", "type": "integer"}, "name": {"description": "商品名称", "type": "string"}, "sec_category_id": {"description": "二级分类ID", "type": "integer"}, "sec_category_name": {"description": "二级分类名称", "type": "string"}, "sort_num": {"description": "在商家分类下的排序号", "type": "integer"}, "status": {"description": "商品状态 0-待上架 1-上架 2-下架", "type": "integer"}, "template_id": {"description": "商品模板ID", "type": "integer"}, "template_name": {"description": "模板名称", "type": "string"}, "thd_category_id": {"description": "三级分类ID", "type": "integer"}, "thd_category_name": {"description": "三级分类名称", "type": "string"}}}, "dto.ProductPriceReq": {"type": "object", "properties": {"currency": {"description": "货币代码 如USD、CNY等", "type": "string"}, "id": {"description": "价格ID，更新时传入", "type": "integer"}, "order_max": {"description": "最大订购量", "type": "integer"}, "order_min": {"description": "最小起订量", "type": "integer"}, "price_max": {"description": "最高价格（分为单位）", "type": "integer"}, "price_min": {"description": "最低价格（分为单位）", "type": "integer"}, "price_unit": {"description": "价格单位 如piece、kg、ton等", "type": "string"}, "product_id": {"description": "商品ID", "type": "integer"}}}, "dto.ProductUpdateReq": {"type": "object", "properties": {"id": {"description": "商品ID\nRequired: true", "type": "integer"}, "merchant_category_id": {"description": "商家商品分类ID", "type": "integer"}, "name": {"description": "商品名称", "type": "string"}, "sort_num": {"description": "在商家分类下的排序号", "type": "integer"}, "status": {"description": "商品状态 0-待上架 1-上架 2-下架", "type": "integer"}}}, "dto.ProductUpdateStatusReq": {"type": "object", "properties": {"id": {"description": "商品ID\nRequired: true", "type": "integer"}, "status": {"description": "商品状态 0-待上架 1-上架 2-下架", "type": "integer"}}}, "dto.RefreshTokenRequest": {"type": "object", "required": ["refresh_token"], "properties": {"refresh_token": {"type": "string"}}}, "dto.RefreshTokenResponse": {"type": "object", "properties": {"access_token": {"type": "string"}, "expires_in": {"type": "integer"}, "refresh_token": {"type": "string"}, "token_type": {"type": "string"}}}, "dto.TemplateAttributeCreateReq": {"type": "object", "required": ["attribute_id", "template_id"], "properties": {"attribute_id": {"description": "属性ID\nRequired: true", "type": "integer"}, "sort_num": {"description": "在模板中的排序号", "type": "integer"}, "template_id": {"description": "模板ID\nRequired: true", "type": "integer"}}}, "dto.TemplateAttributeRes": {"type": "object", "properties": {"attribute": {"description": "属性详细信息", "allOf": [{"$ref": "#/definitions/dto.AttributeRes"}]}, "attribute_id": {"description": "属性ID", "type": "integer"}, "id": {"description": "模板属性关联ID", "type": "integer"}, "sort_num": {"description": "在模板中的排序号", "type": "integer"}, "template_id": {"description": "模板ID", "type": "integer"}}}, "dto.TemplateAttributeUpdateReq": {"type": "object", "properties": {"id": {"description": "模板属性关联ID\nRequired: true", "type": "integer"}, "sort_num": {"description": "在模板中的排序号", "type": "integer"}}}, "dto.TemplateBatchAttributesReq": {"type": "object", "required": ["attribute_ids", "template_id"], "properties": {"attribute_ids": {"description": "属性ID列表\nRequired: true", "type": "array", "items": {"type": "integer"}}, "template_id": {"description": "模板ID\nRequired: true", "type": "integer"}}}, "dto.TemplateCreateReq": {"type": "object", "required": ["name"], "properties": {"name": {"description": "模板名称\nRequired: true", "type": "string"}, "status": {"description": "模板状态 0-禁用 1-启用", "type": "integer"}}}, "dto.TemplateRes": {"type": "object", "properties": {"attributes": {"description": "模板关联的属性列表", "type": "array", "items": {"$ref": "#/definitions/dto.TemplateAttributeRes"}}, "id": {"description": "模板ID", "type": "integer"}, "name": {"description": "模板名称", "type": "string"}, "status": {"description": "模板状态 0-禁用 1-启用", "type": "integer"}}}, "dto.TemplateUpdateReq": {"type": "object", "properties": {"id": {"description": "模板ID\nRequired: true", "type": "integer"}, "name": {"description": "模板名称", "type": "string"}, "status": {"description": "模板状态 0-禁用 1-启用", "type": "integer"}}}, "dto.UserInfo": {"type": "object", "properties": {"create_time": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "phone": {"type": "string"}, "status": {"type": "integer"}, "update_time": {"type": "string"}, "username": {"type": "string"}}}, "dto.UserLoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "dto.UserLoginResponse": {"type": "object", "properties": {"access_token": {"type": "string"}, "expires_in": {"type": "integer"}, "refresh_token": {"type": "string"}, "token_type": {"type": "string"}, "user_info": {"$ref": "#/definitions/dto.UserInfo"}}}, "dto.UserRegisterRequest": {"type": "object", "required": ["email", "password", "username"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 8}, "phone": {"type": "string"}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "dto.UserUpdateRequest": {"type": "object", "properties": {"phone": {"type": "string"}, "username": {"type": "string"}}}, "result.Page": {"type": "object", "properties": {"data": {"description": "分页数据"}, "page": {"description": "Page 分页信息", "type": "integer"}, "pages": {"description": "总页数", "type": "integer"}, "size": {"description": "每页数量", "type": "integer"}, "total": {"description": "总记录数", "type": "integer"}}}, "result.Result": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "msg": {"type": "string"}}}}}