import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getCategories } from '../api/home'

export const useCategoryStore = defineStore('category', () => {
  const categories = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  // 加载分类数据
  const loadCategories = async () => {
    if (categories.value.length > 0) {
      // 如果已经有数据，直接返回
      return categories.value
    }
    
    try {
      loading.value = true
      error.value = null
      
      const response = await getCategories()
      console.log('分类数据API响应:', response)
      
      if (response && response.code === 0) {
        categories.value = response.data || []
        console.log('分类数据已设置:', categories.value)
      } else {
        error.value = response?.msg || '获取分类数据失败'
        console.error('获取分类数据失败:', response)
      }
    } catch (err) {
      error.value = '加载分类数据时发生错误'
      console.error('加载分类数据时发生错误:', err)
    } finally {
      loading.value = false
    }
    
    return categories.value
  }
  
  // 根据ID查找分类
  const findCategoryById = (id, categoryList = categories.value) => {
    for (const category of categoryList) {
      if (category.id === id) return category
      if (category.sub_categories) {
        const found = findCategoryById(id, category.sub_categories)
        if (found) return found
      }
    }
    return null
  }
  
  // 清除分类数据（用于重新加载）
  const clearCategories = () => {
    categories.value = []
    error.value = null
  }
  
  return {
    categories,
    loading,
    error,
    loadCategories,
    findCategoryById,
    clearCategories
  }
})
