<template>
  <div class="product-card" @click="goToProductDetail">
    <!-- 左侧：产品图片 -->
    <div class="product-image">
      <img :src="product.image" :alt="currentLang === 'zh' ? product.name : product.nameEn" />
    </div>
    
    <!-- 中间：产品信息 -->
    <div class="product-info">
      <h3 class="product-name">
        {{ currentLang === 'zh' ? product.name : product.nameEn }}
      </h3>
      
      <div class="price-section">
        <div v-if="product.prices && product.prices.length > 0" class="price-list">
          <div v-for="(priceItem, index) in product.prices" :key="index" class="price-item">
            <span class="price-text">
              {{ currentLang === 'zh' ? '单价：' : 'Unit Price: ' }}{{ priceItem.price_min/100 }} {{ priceItem.currency }}，
              {{ currentLang === 'zh' ? '起订量：' : 'Min Order: ' }}{{ priceItem.order_min }} - {{ priceItem.order_max }}
            </span>
          </div>
        </div>
        <span v-else class="price">{{ product.price || 'N/A' }}</span>
      </div>
      
      <p class="product-description">
        {{ currentLang === 'zh' ? product.description : product.descriptionEn }}
      </p>
      
      <div class="specifications" v-if="product.specifications">
        <h4>{{ currentLang === 'zh' ? '产品规格' : 'Specifications' }}</h4>
        <div class="spec-list">
         {{ product.specifications }}
        </div>
      </div>
    </div>
    
    <!-- 右侧：公司信息 -->
    <div class="company-info" v-if="company">
      <div class="company-header">
        <img :src="company.avatar || '/src/assets/logo.png'" :alt="company.name || ''" class="company-avatar" />
        <div class="company-basic">
          <h4 class="company-name">
            {{ currentLang === 'zh' ? (company.name || '') : (company.nameEn || company.name || '') }}
          </h4>
          <div class="company-rating">
            <div class="stars">
              <span v-for="i in 5" :key="i" class="star" :class="{ filled: i <= Math.floor(company.rating || 0) }">★</span>
              <span class="rating-number">{{ company.rating || 0 }}</span>
            </div>
          </div>
          <div class="company-location">
            <el-icon><LocationFilled /></el-icon>
            <span>{{ currentLang === 'zh' ? (company.location || '') : (company.locationEn || company.location || '') }}</span>
          </div>
          <div class="company-stats">
            <span class="stat-item">
              <el-icon><OfficeBuilding /></el-icon>
              {{ currentLang === 'zh' ? `${company.employeeCount || 0}人` : `${company.employeeCount || 0} employees` }}
            </span>
            <span class="stat-item">
              <el-icon><User /></el-icon>
              {{ currentLang === 'zh' ? `${company.yearsInBusiness || 0}年` : `${company.yearsInBusiness || 0} years` }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="contact-actions">
        <el-button type="primary" @click="contactSupplier" class="contact-btn">
          <el-icon><Phone /></el-icon>
          {{ currentLang === 'zh' ? '立即联系' : 'Contact Now' }}
        </el-button>
        <el-button @click="viewCompany" class="view-company-btn">
          {{ currentLang === 'zh' ? '查看公司' : 'View Company' }}
        </el-button>
      </div>
    </div>
    <div class="company-info-placeholder" v-else>
      <p>{{ currentLang === 'zh' ? '供应商信息暂无' : 'Supplier info not available' }}</p>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { LocationFilled, OfficeBuilding, User, Phone } from '@element-plus/icons-vue'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true
    },
    company: {
      type: Object,
      required: false,
      default: null
    }
  },
  setup(props) {
    const languageStore = useLanguageStore()
    const router = useRouter()
    const currentLang = computed(() => languageStore.currentLang)

    // 调试：打印产品数据
    console.log('ProductCard 接收到的产品数据:', props.product)
    console.log('产品价格数组:', props.product.prices)
    
    const goToProductDetail = () => {
      router.push(`/product/${props.product.id}`)
    }
    
    const contactSupplier = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      if (props.company && props.company.id) {
        router.push(`/contact-supplier/${props.company.id}`)
      } else {
        console.warn('无法联系供应商：缺少供应商信息')
      }
    }
    
    const viewCompany = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      if (props.company && props.company.id) {
        router.push(`/company/${props.company.id}`)
      } else {
        console.warn('无法查看公司：缺少公司信息')
      }
    }
    
    return {
      currentLang,
      goToProductDetail,
      contactSupplier,
      viewCompany
    }
  }
}
</script>

<style lang="scss" scoped>
.product-card {
  display: grid;
  grid-template-columns: 280px 1fr 320px;
  gap: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
}

.product-image {
  img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e8eaed;
  }
}

.product-info {
  .product-name {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
  }
  
  .price-section {
    margin-bottom: 16px;

    .price {
      font-size: 24px;
      font-weight: 700;
      color: #2563eb;
    }

    .price-list {
      .price-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .price-text {
          font-size: 16px;
          font-weight: 600;
          color: #eb2535;
          line-height: 1.4;
          display: block;
        }
      }
    }
  }
  
  .order-info {
    display: flex;
    gap: 24px;
    margin-bottom: 16px;
    
    .min-order, .stock {
      font-size: 14px;
      color: #6b7280;
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
    }
  }
  
  .product-description {
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 16px;
    font-size: 14px;
  }
  
  .specifications {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #374151;
    }
    
    .spec-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .spec-item {
        background: #eff6ff;
        color: #1e40af;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}

.company-info {
  border-left: 2px solid #f0f2f5;
  padding-left: 20px;
  
  .company-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .company-avatar {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .company-basic {
      flex: 1;
      
      .company-name {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
      
      .company-rating {
        .stars {
          display: flex;
          align-items: center;
          gap: 2px;
          
          .star {
            color: #d1d5db;
            font-size: 14px;
            
            &.filled {
              color: #fbbf24;
            }
          }
          
          .rating-number {
            margin-left: 6px;
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  .company-details {
    margin-bottom: 20px;
    
    .detail-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      font-size: 13px;
      color: #6b7280;
      
      .el-icon {
        color: #9ca3af;
        font-size: 14px;
      }
    }
    
    .certifications {
      margin-top: 12px;
      
      .cert-label {
        display: block;
        font-size: 12px;
        color: #6b7280;
        margin-bottom: 6px;
      }
      
      .cert-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        
        .cert-tag {
          background: #10b981;
          color: white;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 10px;
          font-weight: 500;
        }
      }
    }
  }
  
  .contact-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .contact-btn {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border: none;
      padding: 10px;
      border-radius: 6px;
      font-weight: 600;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
      }
    }
    
    .view-company-btn {
      margin:0 !important;
      border: 1px solid #d1d5db;
      color: #6b7280;
      font-size: 13px;
      padding: 8px;
      
      &:hover {
        border-color: #2563eb;
        color: #2563eb;
      }
    }
  }
}

.company-info-placeholder {
  border-left: 2px solid #f0f2f5;
  padding-left: 20px;
  padding: 24px;
  background: #f9fafb;
  border-radius: 12px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

@media (max-width: 1200px) {
  .product-card {
    grid-template-columns: 220px 1fr 280px;
    gap: 16px;
    padding: 20px;
  }
  
  .product-image img {
    height: 160px;
  }
}

// 笔记本屏幕优化 (1024px - 1366px)
@media (max-width: 1366px) and (min-width: 1025px) {
  .product-card {
    grid-template-columns: 240px 1fr 300px;
    gap: 20px;
    padding: 20px;
  }
  
  .product-image img {
    height: 180px;
  }
  
  .product-info {
    .product-name {
      font-size: 18px;
    }
    
    .price-section .price {
      font-size: 22px;
    }
    
    .product-description {
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  .company-info {
    .company-header .company-basic .company-name {
      font-size: 15px;
    }
    
    .company-details .detail-item {
      font-size: 12px;
    }
  }
}

// 小笔记本优化 (969px - 1024px)
@media (max-width: 1024px) and (min-width: 969px) {
  .product-card {
    grid-template-columns: 200px 1fr 260px;
    gap: 16px;
    padding: 18px;
  }
  
  .product-image img {
    height: 150px;
  }
  
  .product-info {
    .product-name {
      font-size: 17px;
    }
    
    .price-section .price {
      font-size: 20px;
    }
    
    .order-info {
      gap: 16px;
      
      .min-order, .stock {
        font-size: 13px;
        padding: 3px 6px;
      }
    }
    
    .product-description {
      font-size: 13px;
    }
    
    .specifications {
      .spec-list .spec-item {
        font-size: 11px;
        padding: 3px 6px;
      }
    }
  }
  
  .company-info {
    padding-left: 16px;
    
    .company-header {
      .company-avatar {
        width: 40px;
        height: 40px;
      }
      
      .company-basic .company-name {
        font-size: 14px;
      }
    }
    
    .company-details .detail-item {
      font-size: 12px;
      margin-bottom: 6px;
    }
    
    .contact-actions {
      .contact-btn {
        padding: 8px;
        font-size: 13px;
      }
      
      .view-company-btn {
        padding: 6px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 968px) {
  .product-card {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .product-image {
    text-align: center;
    
    img {
      width: 200px;
      height: 150px;
    }
  }
  
  .company-info {
    border-left: none;
    border-top: 2px solid #f0f2f5;
    padding-left: 0;
    padding-top: 20px;
  }
}
</style> 